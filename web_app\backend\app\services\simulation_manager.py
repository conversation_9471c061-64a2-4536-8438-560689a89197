import asyncio
import json
from typing import Dict, Optional
from concurrent.futures import Future
from app.services.connection_manager import manager


class LogStream:
    def __init__(self, simulation_id: int, manager: "SimulationManager"):
        self.simulation_id = simulation_id
        self.manager = manager

    def write(self, text: str):
        if text.strip():
            # Format the message similarly to how the logger does
            log_entry = {
                "level": "INFO",
                "message": text.strip(),
            }
            self.manager.broadcast_sync(json.dumps(log_entry), self.simulation_id)

    def flush(self):
        pass


class SimulationManager:
    def __init__(self) -> None:
        self.queues: Dict[int, asyncio.Queue] = {}
        self.log_consumers: Dict[int, Future] = {}
        self.loop: Optional[asyncio.AbstractEventLoop] = None

    def _ensure_loop(self):
        """确保事件循环已初始化"""
        if self.loop is None:
            try:
                self.loop = asyncio.get_running_loop()
            except RuntimeError:
                # 如果没有运行中的循环，创建一个新的
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)

    def add_task_sync(self, simulation_id: int):
        self._ensure_loop()
        if simulation_id not in self.queues and self.loop:
            self.queues[simulation_id] = asyncio.Queue()
            consumer_task = asyncio.run_coroutine_threadsafe(
                self._log_consumer(simulation_id), self.loop
            )
            self.log_consumers[simulation_id] = consumer_task

    async def _log_consumer(self, simulation_id: int):
        queue = self.queues.get(simulation_id)
        if not queue:
            print(f"SIM MANAGER [sim:{simulation_id}]: No queue found for log consumer")
            return

        print(f"SIM MANAGER [sim:{simulation_id}]: Log consumer started.")
        try:
            while True:
                try:
                    # Use a timeout to make the loop responsive to cancellation
                    message = await asyncio.wait_for(queue.get(), timeout=1.0)
                    if message is None:
                        print(
                            f"SIM MANAGER [sim:{simulation_id}]: Log consumer stopping."
                        )
                        break
                    print(
                        f"SIM MANAGER [sim:{simulation_id}]: Dequeued log: {message}. Broadcasting..."
                    )
                    await manager.broadcast(message, simulation_id)
                    queue.task_done()
                except asyncio.TimeoutError:
                    # The timeout allows us to break out of queue.get() and check for cancellation
                    pass
                except Exception as e:
                    print(
                        f"SIM MANAGER [sim:{simulation_id}]: Error in log consumer: {e}"
                    )
                    break
        except asyncio.CancelledError:
            print(f"SIM MANAGER [sim:{simulation_id}]: Log consumer cancelled.")
        finally:
            print(f"SIM MANAGER [sim:{simulation_id}]: Log consumer finished.")

    def remove_task_sync(self, simulation_id: int):
        print(f"SIM MANAGER [sim:{simulation_id}]: Starting cleanup...")

        if simulation_id in self.queues:
            consumer = self.log_consumers.pop(simulation_id, None)
            if consumer and self.loop:
                try:
                    # Put sentinel value to stop consumer
                    self._ensure_loop()
                    asyncio.run_coroutine_threadsafe(
                        self.queues[simulation_id].put(None), self.loop
                    )
                    print(
                        f"SIM MANAGER [sim:{simulation_id}]: Sent stop signal to log consumer"
                    )

                    # 强制取消任务，不等待
                    consumer.cancel()
                    print(f"SIM MANAGER [sim:{simulation_id}]: Consumer cancelled")
                except Exception as e:
                    print(
                        f"SIM MANAGER [sim:{simulation_id}]: Error during consumer cleanup: {e}"
                    )

            # 清理队列
            try:
                del self.queues[simulation_id]
                print(f"SIM MANAGER [sim:{simulation_id}]: Queue removed")
            except Exception as e:
                print(f"SIM MANAGER [sim:{simulation_id}]: Error removing queue: {e}")
        else:
            print(f"SIM MANAGER [sim:{simulation_id}]: No queue found to cleanup")

        print(f"SIM MANAGER [sim:{simulation_id}]: Cleanup completed")

    def broadcast_sync(self, message: str, simulation_id: int):
        if simulation_id in self.queues and self.loop:
            self._ensure_loop()
            try:
                asyncio.run_coroutine_threadsafe(
                    self.queues[simulation_id].put(message), self.loop
                )
                # 不等待完成，避免阻塞
                print(f"SIM MANAGER [sim:{simulation_id}]: Queued message: {message}")
            except Exception as e:
                print(
                    f"SIM MANAGER [sim:{simulation_id}]: Error broadcasting message: {e}"
                )

    def get_log_stream(self, simulation_id: int) -> LogStream:
        return LogStream(simulation_id, self)

    def cancel_all_tasks(self):
        """取消所有任务"""
        print("SIM MANAGER: Cancelling all tasks...")

        # 发送停止信号给所有消费者
        for simulation_id in list(self.queues.keys()):
            try:
                self._ensure_loop()
                asyncio.run_coroutine_threadsafe(
                    self.queues[simulation_id].put(None), self.loop
                )
                print(f"SIM MANAGER [sim:{simulation_id}]: Sent stop signal")
            except Exception as e:
                print(
                    f"SIM MANAGER [sim:{simulation_id}]: Error sending stop signal: {e}"
                )

        # 取消所有消费者任务
        for simulation_id, consumer in list(self.log_consumers.items()):
            try:
                consumer.cancel()
                print(f"SIM MANAGER [sim:{simulation_id}]: Consumer cancelled")
            except Exception as e:
                print(
                    f"SIM MANAGER [sim:{simulation_id}]: Error cancelling consumer: {e}"
                )

        # 清理所有队列和消费者
        self.queues.clear()
        self.log_consumers.clear()
        print("SIM MANAGER: All tasks cancelled")


simulation_manager = SimulationManager()
