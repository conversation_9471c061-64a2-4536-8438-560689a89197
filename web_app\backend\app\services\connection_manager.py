from fastapi import WebSocket
from typing import Dict, List
import logging

logger = logging.getLogger(__name__)


class ConnectionManager:
    def __init__(self) -> None:
        self.active_connections: Dict[int, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, simulation_id: int):
        await websocket.accept()
        if simulation_id not in self.active_connections:
            self.active_connections[simulation_id] = []
        self.active_connections[simulation_id].append(websocket)
        logger.info(
            f"WebSocket connected for simulation {simulation_id}, total connections: {len(self.active_connections[simulation_id])}"
        )

    def disconnect(self, websocket: WebSocket, simulation_id: int):
        if simulation_id in self.active_connections:
            try:
                self.active_connections[simulation_id].remove(websocket)
                logger.info(f"WebSocket disconnected for simulation {simulation_id}")
                if not self.active_connections[simulation_id]:
                    del self.active_connections[simulation_id]
                    logger.info(
                        f"All connections closed for simulation {simulation_id}"
                    )
            except ValueError:
                # WebSocket was already removed
                logger.warning(
                    f"Attempted to remove non-existent WebSocket for simulation {simulation_id}"
                )

    async def broadcast(self, message: str, simulation_id: int):
        if simulation_id in self.active_connections:
            logger.debug(
                f"Broadcasting message to {len(self.active_connections[simulation_id])} client(s) for simulation {simulation_id}"
            )

            # 创建连接副本以避免在迭代时修改列表
            connections = self.active_connections[simulation_id].copy()
            disconnected_connections = []

            for connection in connections:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.warning(f"Failed to send message to WebSocket: {e}")
                    disconnected_connections.append(connection)

            # 清理断开的连接
            for connection in disconnected_connections:
                self.disconnect(connection, simulation_id)
        else:
            logger.debug(
                f"No active connections to broadcast to for simulation {simulation_id}"
            )


manager = ConnectionManager()
