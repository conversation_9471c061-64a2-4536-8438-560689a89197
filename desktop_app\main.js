const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tray, Menu, dialog, globalShortcut, ipcMain } = require('electron');
const { spawn } = require('child_process');
const path = require('path');
const portfinder = require('portfinder');
const WebSocket = require('ws');
const { setupSecurity, getSecureWindowOptions } = require('./security');

// 设置开发模式环境变量以支持开发者工具
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
}

// 启用开发者工具支持
app.commandLine.appendSwitch('enable-logging');
app.commandLine.appendSwitch('remote-debugging-port', '9222');

let pythonProcess = null;
let splash;
let tray = null;
let win = null;

// WebSocket连接管理器
const websockets = new Map();

// 设置IPC处理器
function setupWebSocketIPC() {
  // 创建WebSocket连接
  ipcMain.on('create-websocket', (event, id, url) => {
    try {
      const ws = new WebSocket(url);
      websockets.set(id, ws);

      ws.on('open', () => {
        event.reply(`websocket-${id}`, 'open');
      });

      ws.on('message', (data) => {
        event.reply(`websocket-${id}`, 'message', data.toString());
      });

      ws.on('close', (code, reason) => {
        event.reply(`websocket-${id}`, 'close', { code, reason: reason.toString() });
        websockets.delete(id);
      });

      ws.on('error', (error) => {
        event.reply(`websocket-${id}`, 'error', error.message);
        websockets.delete(id);
      });
    } catch (error) {
      event.reply(`websocket-${id}`, 'error', error.message);
    }
  });

  // 发送WebSocket消息
  ipcMain.on('websocket-send', (_, id, data) => {
    const ws = websockets.get(id);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(data);
    }
  });

  // 关闭WebSocket连接
  ipcMain.on('websocket-close', (_, id, code, reason) => {
    const ws = websockets.get(id);
    if (ws) {
      ws.close(code, reason);
      websockets.delete(id);
    }
  });
}

const createWindow = async () => {
  // 创建启动窗口
  splash = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    center: true,
    resizable: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
  });
  splash.loadFile(path.join(__dirname, 'splash.html'));

  const port = await portfinder.getPortPromise();

  const backendPath = app.isPackaged
    ? path.join(process.resourcesPath, 'bin', 'backend_server', 'backend_server.exe')
    : path.join(__dirname, 'bin', 'backend_server', 'backend_server.exe');

  pythonProcess = spawn(backendPath, ['--port', port]);

  pythonProcess.stdout.on('data', (data) => {
    console.log(`Python stdout: ${data}`);
  });

  pythonProcess.stderr.on('data', (data) => {
    console.error(`Python stderr: ${data}`);
  });

  win = new BrowserWindow(getSecureWindowOptions({
    width: 1440,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    show: false, // 默认隐藏
    webPreferences: {
      preload: path.join(__dirname, 'preload.js')
    },
    title: '数码一体机凸轮优化仿真',
    titleBarStyle: 'default'
  }));

  // 等待窗口准备就绪后显示
  win.once('ready-to-show', () => {
    splash.destroy();
    win.show();

    // 开发环境下可以打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      win.webContents.openDevTools();
    }
  });

  // 添加右键菜单支持开发者工具
  win.webContents.on('context-menu', (_, params) => {
    const menu = Menu.buildFromTemplate([
      {
        label: '检查元素',
        click: () => {
          win.webContents.inspectElement(params.x, params.y);
        }
      },
      {
        label: '开发者工具',
        accelerator: 'F12',
        click: () => {
          win.webContents.toggleDevTools();
        }
      },
      { type: 'separator' },
      {
        label: '刷新',
        accelerator: 'F5',
        click: () => {
          win.webContents.reload();
        }
      },
      {
        label: '强制刷新',
        accelerator: 'Ctrl+F5',
        click: () => {
          win.webContents.reloadIgnoringCache();
        }
      }
    ]);
    menu.popup();
  });

  // 处理窗口加载错误
  win.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    console.error('Failed to load:', errorCode, errorDescription);
    // 可以显示错误页面或重试逻辑
  });

  // 等待服务器启动后加载URL
  setTimeout(async () => {
    try {
      await win.loadURL(`http://127.0.0.1:${port}`);
    } catch (error) {
      console.error('Failed to load URL:', error);
    }
  }, 3000);

  win.on('close', (event) => {
    if (!app.isQuitting) {
      event.preventDefault();
      dialog.showMessageBox(win, {
        type: 'question',
        buttons: ['最小化到托盘', '退出程序', '取消'],
        defaultId: 0,
        cancelId: 2,
        title: '数码一体机凸轮优化仿真',
        message: '您想如何操作？',
        detail: '• 最小化到托盘：应用将在后台继续运行\n• 退出程序：完全关闭应用\n• 取消：返回应用',
        icon: path.join(__dirname, 'sim.png')
      }).then(result => {
        if (result.response === 1) { // 退出程序
          app.isQuitting = true;
          app.quit();
        } else if (result.response === 0) { // 最小化到托盘
          win.hide();
          // 首次最小化时显示提示
          if (!win.hasShownTrayNotification) {
            tray.displayBalloon({
              title: '应用已最小化',
              content: '应用正在后台运行，点击托盘图标可重新打开',
              icon: path.join(__dirname, 'sim.png')
            });
            win.hasShownTrayNotification = true;
          }
        }
        // response === 2 是取消，不做任何操作
      });
    }
  });
};

app.whenReady().then(() => {
  // 设置WebSocket IPC处理器
  setupWebSocketIPC();

  // 应用安全设置（开发模式下更宽松）
  setupSecurity();

  // 配置session以支持开发者工具
  const { session } = require('electron');

  // 允许开发者工具相关的协议和域名
  session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
    // 允许开发者工具的请求
    if (details.url.includes('devtools') || details.url.includes('chrome-devtools-frontend')) {
      callback({ cancel: false });
    } else {
      callback({ cancel: false });
    }
  });

  // 设置权限以允许开发者工具
  session.defaultSession.setPermissionRequestHandler((_, permission, callback) => {
    // 允许开发者工具需要的权限
    const allowedPermissions = ['clipboard-read', 'clipboard-write'];
    callback(allowedPermissions.includes(permission));
  });

  createWindow();

  // 注册全局快捷键
  globalShortcut.register('F12', () => {
    if (win && !win.isDestroyed()) {
      win.webContents.toggleDevTools();
    }
  });
  
  globalShortcut.register('F5', () => {
    if (win && !win.isDestroyed()) {
      win.webContents.reload();
    }
  });
  
  globalShortcut.register('CommandOrControl+R', () => {
    if (win && !win.isDestroyed()) {
      win.webContents.reload();
    }
  });
  
  globalShortcut.register('CommandOrControl+Shift+R', () => {
    if (win && !win.isDestroyed()) {
      win.webContents.reloadIgnoringCache();
    }
  });
  
  globalShortcut.register('CommandOrControl+Shift+I', () => {
    if (win && !win.isDestroyed()) {
      win.webContents.toggleDevTools();
    }
  });

  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, 'sim.png')
    : path.join(__dirname, 'sim.png');

  tray = new Tray(iconPath);
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '数码一体机凸轮优化仿真',
      enabled: false
    },
    { type: 'separator' },
    {
      label: '显示主窗口',
      click: () => {
        win.show();
        win.focus();
      }
    },
    {
      label: '开发者工具',
      click: () => {
        if (win && !win.isDestroyed()) {
          win.webContents.toggleDevTools();
        }
      }
    },
    { type: 'separator' },
    {
      label: '关于',
      click: () => {
        dialog.showMessageBox(win, {
          type: 'info',
          title: '关于',
          message: '数码一体机凸轮优化仿真',
          detail: '版本: 1.0.0',
          icon: iconPath
        });
      }
    },
    {
      label: '退出应用',
      click: () => {
        app.isQuitting = true;
        app.quit();
      }
    }
  ]);
  tray.setToolTip('数码一体机凸轮优化仿真');
  tray.setContextMenu(contextMenu);

  // Add these event listeners
  tray.on('click', () => {
    win.show();
  });

  tray.on('double-click', () => {
    win.show();
  });

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else {
      win.show();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Do not quit, allow to run in background
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts.
  globalShortcut.unregisterAll();

  // 清理所有WebSocket连接
  websockets.forEach((ws, id) => {
    try {
      ws.close();
    } catch (error) {
      console.error(`Error closing WebSocket ${id}:`, error);
    }
  });
  websockets.clear();

  if (pythonProcess) {
    if (process.platform === 'win32') {
      spawn('taskkill', ['/pid', pythonProcess.pid, '/f', '/t']);
    } else {
      pythonProcess.kill();
    }
  }
});
