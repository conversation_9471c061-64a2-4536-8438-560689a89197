const { app, BrowserWindow, Tray, <PERSON>u, dialog, globalShortcut } = require('electron');
const { spawn } = require('child_process');
const path = require('path');
const portfinder = require('portfinder');
const { setupSecurity, getSecureWindowOptions } = require('./security');

// 设置开发模式环境变量以支持开发者工具
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'production';
}

// 只在开发模式下启用日志和调试
if (process.env.NODE_ENV === 'development') {
  app.commandLine.appendSwitch('enable-logging');
  app.commandLine.appendSwitch('remote-debugging-port', '9222');
}

let pythonProcess = null;
let splash;
let tray = null;
let win = null;

const createWindow = async () => {
  // 创建启动窗口
  splash = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    center: true,
    resizable: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
  });
  splash.loadFile(path.join(__dirname, 'splash.html'));

  const port = await portfinder.getPortPromise();

  const backendPath = app.isPackaged
    ? path.join(process.resourcesPath, 'bin', 'backend_server', 'backend_server.exe')
    : path.join(__dirname, 'bin', 'backend_server', 'backend_server.exe');

  // 配置spawn选项以隐藏控制台窗口
  const spawnOptions = {
    windowsHide: true, // Windows下隐藏控制台窗口
    stdio: process.env.NODE_ENV === 'development' ? 'pipe' : 'ignore' // 生产环境下忽略输出
  };

  pythonProcess = spawn(backendPath, ['--port', port], spawnOptions);

  // 只在开发模式下监听输出
  if (process.env.NODE_ENV === 'development') {
    pythonProcess.stdout.on('data', (data) => {
      console.log(`Python stdout: ${data}`);
    });

    pythonProcess.stderr.on('data', (data) => {
      console.error(`Python stderr: ${data}`);
    });
  }

  win = new BrowserWindow(getSecureWindowOptions({
    width: 1440,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    show: false, // 默认隐藏
    webPreferences: {
      preload: path.join(__dirname, 'preload.js')
    },
    title: '凸轮优化仿真',
    titleBarStyle: 'default'
  }));

  // 等待窗口准备就绪后显示
  win.once('ready-to-show', () => {
    splash.destroy();
    win.show();

    // 只在开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      win.webContents.openDevTools();
    }
  });

  // 添加右键菜单支持开发者工具
  win.webContents.on('context-menu', (_, params) => {
    const menu = Menu.buildFromTemplate([
      {
        label: '检查元素',
        click: () => {
          win.webContents.inspectElement(params.x, params.y);
        }
      },
      {
        label: '开发者工具',
        accelerator: 'F12',
        click: () => {
          win.webContents.toggleDevTools();
        }
      },
      { type: 'separator' },
      {
        label: '刷新',
        accelerator: 'F5',
        click: () => {
          win.webContents.reload();
        }
      },
      {
        label: '强制刷新',
        accelerator: 'Ctrl+F5',
        click: () => {
          win.webContents.reloadIgnoringCache();
        }
      }
    ]);
    menu.popup();
  });

  // 处理窗口加载错误
  win.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    console.error('Failed to load:', errorCode, errorDescription);
    // 可以显示错误页面或重试逻辑
  });

  // 等待服务器启动后加载URL
  setTimeout(async () => {
    try {
      await win.loadURL(`http://127.0.0.1:${port}`);
    } catch (error) {
      console.error('Failed to load URL:', error);
    }
  }, 3000);

  win.on('close', (event) => {
    if (!app.isQuitting) {
      event.preventDefault();
      dialog.showMessageBox(win, {
        type: 'question',
        buttons: ['最小化到托盘', '退出程序', '取消'],
        defaultId: 0,
        cancelId: 2,
        title: '凸轮优化仿真',
        message: '您想如何操作？',
        detail: '• 最小化到托盘：应用将在后台继续运行\n• 退出程序：完全关闭应用\n• 取消：返回应用',
        icon: path.join(__dirname, 'sim.png')
      }).then(result => {
        if (result.response === 1) { // 退出程序
          app.isQuitting = true;
          app.quit();
        } else if (result.response === 0) { // 最小化到托盘
          win.hide();
          // 首次最小化时显示提示
          if (!win.hasShownTrayNotification) {
            tray.displayBalloon({
              title: '应用已最小化',
              content: '应用正在后台运行，点击托盘图标可重新打开',
              icon: path.join(__dirname, 'sim.png')
            });
            win.hasShownTrayNotification = true;
          }
        }
        // response === 2 是取消，不做任何操作
      });
    }
  });
};

app.whenReady().then(() => {
  // 应用安全设置（开发模式下更宽松）
  setupSecurity();

  // 只在开发模式下配置开发者工具支持
  if (process.env.NODE_ENV === 'development') {
    const { session } = require('electron');

    // 允许开发者工具相关的协议和域名
    session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      // 允许开发者工具的请求
      if (details.url.includes('devtools') || details.url.includes('chrome-devtools-frontend')) {
        callback({ cancel: false });
      } else {
        callback({ cancel: false });
      }
    });

    // 设置权限以允许开发者工具
    session.defaultSession.setPermissionRequestHandler((_, permission, callback) => {
      // 允许开发者工具需要的权限
      const allowedPermissions = ['clipboard-read', 'clipboard-write'];
      callback(allowedPermissions.includes(permission));
    });
  }

  createWindow();

  // 只在开发模式下注册开发者快捷键
  if (process.env.NODE_ENV === 'development') {
    globalShortcut.register('F12', () => {
      if (win && !win.isDestroyed()) {
        win.webContents.toggleDevTools();
      }
    });

    globalShortcut.register('F5', () => {
      if (win && !win.isDestroyed()) {
        win.webContents.reload();
      }
    });

    globalShortcut.register('CommandOrControl+R', () => {
      if (win && !win.isDestroyed()) {
        win.webContents.reload();
      }
    });

    globalShortcut.register('CommandOrControl+Shift+R', () => {
      if (win && !win.isDestroyed()) {
        win.webContents.reloadIgnoringCache();
      }
    });

    globalShortcut.register('CommandOrControl+Shift+I', () => {
      if (win && !win.isDestroyed()) {
        win.webContents.toggleDevTools();
      }
    });
  }

  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, 'sim.png')
    : path.join(__dirname, 'sim.png');

  tray = new Tray(iconPath);

  // 构建托盘菜单，开发模式下包含开发者工具
  const menuTemplate = [
    {
      label: '凸轮优化仿真',
      enabled: false
    },
    { type: 'separator' },
    {
      label: '显示主窗口',
      click: () => {
        win.show();
        win.focus();
      }
    }
  ];

  // 只在开发模式下添加开发者工具菜单项
  if (process.env.NODE_ENV === 'development') {
    menuTemplate.push({
      label: '开发者工具',
      click: () => {
        if (win && !win.isDestroyed()) {
          win.webContents.toggleDevTools();
        }
      }
    });
  }

  menuTemplate.push(
    { type: 'separator' },
    {
      label: '关于',
      click: () => {
        dialog.showMessageBox(win, {
          type: 'info',
          title: '关于',
          message: '凸轮优化仿真',
          detail: '版本: 1.0.0',
          icon: iconPath
        });
      }
    },
    {
      label: '退出应用',
      click: () => {
        app.isQuitting = true;
        app.quit();
      }
    }
  );

  const contextMenu = Menu.buildFromTemplate(menuTemplate);
  tray.setToolTip('凸轮优化仿真');
  tray.setContextMenu(contextMenu);

  // Add these event listeners
  tray.on('click', () => {
    win.show();
  });

  tray.on('double-click', () => {
    win.show();
  });

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else {
      win.show();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Do not quit, allow to run in background
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts.
  globalShortcut.unregisterAll();

  if (pythonProcess) {
    if (process.platform === 'win32') {
      spawn('taskkill', ['/pid', pythonProcess.pid, '/f', '/t']);
    } else {
      pythonProcess.kill();
    }
  }
});
