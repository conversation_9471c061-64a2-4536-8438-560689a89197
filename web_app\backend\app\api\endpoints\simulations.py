from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List
from app.schemas.simulation import SimulationCreate, Simulation, SimulationInfo
from app.models.simulation import (
    Simulation as DBSimulation,
    SimulationResult as DBResult,
)
from app.db.session import SessionLocal
import asyncio
from app.simulation import hexagon_simulation
from app.core.logging import get_simulation_logger
from app.services.simulation_manager import simulation_manager
import numpy as np
import pandas as pd
import io

router = APIRouter()


class ExportOptions(BaseModel):
    fields: List[str] = ["theta_deg", "S_total"]
    normalize_total: bool = True


async def run_simulation_task(simulation_id: int, params: dict):
    """
    The background task that runs the simulation and saves the results.
    """
    logger = get_simulation_logger(simulation_id)
    db = SessionLocal()

    try:
        db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
        if not db_sim:
            logger.error(f"Simulation {simulation_id} not found in database")
            return

        # 确保队列存在
        queue = simulation_manager.queues.get(simulation_id)
        if not queue:
            logger.error(f"No queue found for simulation {simulation_id}")
            db_sim.status = "failed"
            db.commit()
            return

        logger.info("Simulation started.")
        db_sim.status = "running"
        db.commit()
        simulation_manager.broadcast_sync(f"status:{db_sim.status}", simulation_id)

        results = await asyncio.to_thread(
            hexagon_simulation.run_simulation, params, queue, simulation_manager.loop
        )

        for key, value in results.items():
            if isinstance(value, np.ndarray):
                results[key] = value.tolist()

        summary = {
            "final_layer_number": int(results["layer_numbers"][-1]),
            "max_accumulated_thickness_mm": float(results["accumulated_thickness"][-1]),
            "total_film_consumption_mm": float(results["S_total"][-1]),
        }

        db_result = DBResult(
            simulation_id=simulation_id,
            results_data=results,
            summary_data=summary,
        )
        db.add(db_result)

        db_sim.status = "completed"
        db.commit()
        logger.info("Simulation completed successfully.")

        # 立即广播完成状态
        simulation_manager.broadcast_sync(f"status:{db_sim.status}", simulation_id)

        # 等待一小段时间确保消息发送
        await asyncio.sleep(0.1)

    except Exception as e:
        import traceback

        traceback.print_exc()
        db_sim.status = "failed"
        db.commit()
        logger.error(f"Simulation {simulation_id} failed: {e}")
        simulation_manager.broadcast_sync(f"status:{db_sim.status}", simulation_id)

        # 等待一小段时间确保消息发送
        await asyncio.sleep(0.1)
    finally:
        db.close()
        # 延迟清理，确保状态消息已发送
        await asyncio.sleep(0.5)
        simulation_manager.remove_task_sync(simulation_id)
        logger.info(f"Simulation {simulation_id} cleanup completed")


@router.post("/", response_model=Simulation, status_code=201)
async def create_simulation(
    simulation: SimulationCreate, background_tasks: BackgroundTasks
):
    """
    Create a new simulation and start it in the background.
    """
    db = SessionLocal()
    db_sim = DBSimulation(status="pending", input_parameters=simulation.model_dump())
    db.add(db_sim)
    db.commit()
    db.refresh(db_sim)

    simulation_manager.add_task_sync(db_sim.id)
    background_tasks.add_task(run_simulation_task, db_sim.id, simulation.model_dump())

    db.close()
    return db_sim


@router.get("/", response_model=list[SimulationInfo])
def read_simulations(skip: int = 0, limit: int = 100):
    """
    Retrieve a list of simulations with essential information.
    """
    db = SessionLocal()
    simulations = (
        db.query(DBSimulation)
        .order_by(DBSimulation.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )
    db.close()
    return simulations


@router.get("/{simulation_id}", response_model=Simulation)
def read_simulation(simulation_id: int):
    """
    Get a specific simulation by ID.
    """
    db = SessionLocal()
    db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
    db.close()
    if db_sim is None:
        raise HTTPException(status_code=404, detail="Simulation not found")
    return db_sim


@router.get("/{simulation_id}/status")
def get_simulation_status(simulation_id: int):
    """
    Get the status of a specific simulation.
    """
    db = SessionLocal()
    db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
    db.close()
    if db_sim is None:
        raise HTTPException(status_code=404, detail="Simulation not found")
    return {"status": db_sim.status}


@router.get("/{simulation_id}/results")
def get_simulation_results(simulation_id: int):
    """
    Get the results of a specific simulation.
    """
    db = SessionLocal()
    db_result = (
        db.query(DBResult).filter(DBResult.simulation_id == simulation_id).first()
    )
    db.close()
    if db_result is None:
        raise HTTPException(status_code=404, detail="Results not found")
    return db_result.results_data


@router.post("/{simulation_id}/export")
def export_simulation_results(simulation_id: int, options: ExportOptions):
    """
    Export simulation results to an Excel file with customizable options.
    """
    db = SessionLocal()
    db_result = (
        db.query(DBResult).filter(DBResult.simulation_id == simulation_id).first()
    )
    db.close()
    if db_result is None:
        raise HTTPException(status_code=404, detail="Results not found")

    results_data = db_result.results_data

    field_mapping = {
        "theta_deg": "旋转角度(°)",
        "S_total": "总长度(mm)",
        "L": "切线长度(mm)",
        "S": "包覆长度(mm)",
        "accumulated_thickness": "累积厚度(mm)",
        "layer_numbers": "层数",
        "layer_consumption": "每层消耗(mm)",
    }

    selected_data = {}
    for field in options.fields:
        if field in results_data and isinstance(results_data[field], list):
            data = results_data[field].copy()

            if field == "S_total" and options.normalize_total and len(data) > 0:
                first_value = data[0] if data[0] is not None else 0
                data = [x - first_value if x is not None else None for x in data]

            chinese_name = field_mapping.get(field, field)
            selected_data[chinese_name] = data

    if not selected_data:
        raise HTTPException(status_code=400, detail="No valid fields selected")

    max_len = max(len(v) for v in selected_data.values() if isinstance(v, list))
    for key, value in selected_data.items():
        if isinstance(value, list):
            len_diff = max_len - len(value)
            if len_diff > 0:
                selected_data[key] = value + [None] * len_diff

    df = pd.DataFrame(selected_data)

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="仿真数据", index=False)

    output.seek(0)

    headers = {
        "Content-Disposition": f'attachment; filename="simulation_{simulation_id}_results.xlsx"'
    }
    return StreamingResponse(
        output,
        headers=headers,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )


@router.delete("/{simulation_id}", status_code=204)
async def delete_simulation(simulation_id: int):
    """
    Delete a simulation and its results.
    """
    db = SessionLocal()
    db_sim = db.query(DBSimulation).filter(DBSimulation.id == simulation_id).first()
    if db_sim is None:
        db.close()
        raise HTTPException(status_code=404, detail="Simulation not found")

    # Stop any running simulation task before deleting
    simulation_manager.remove_task_sync(simulation_id)

    db.query(DBResult).filter(DBResult.simulation_id == simulation_id).delete()

    db.delete(db_sim)
    db.commit()
    db.close()
    return
