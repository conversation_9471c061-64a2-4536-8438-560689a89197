import sys
import os
import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from app.api.api import api_router
from app.api.endpoints import websockets
from app.core.config import API_V1_STR
from app.db.init_db import init_db
from app.db.session import SessionLocal
from app.services.simulation_manager import simulation_manager

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    logger.info("Application starting up...")
    db = SessionLocal()
    try:
        init_db(db)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise
    finally:
        db.close()

    yield

    # 关闭时
    logger.info("Application shutting down...")
    try:
        simulation_manager.cancel_all_tasks()
        logger.info("All simulation tasks cancelled")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


app = FastAPI(title="Hexagon Simulation API", lifespan=lifespan)


# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=API_V1_STR)
app.include_router(websockets.router)

# Mount the static files directory
if getattr(sys, "frozen", False):
    # If the application is run as a bundle, the PyInstaller bootloader
    # extends the sys module by a flag frozen=True and sets the app
    # path into variable _MEIPASS'.
    base_path = sys._MEIPASS  # type: ignore
    static_path = os.path.join(base_path, "dist")
else:
    static_path = "dist"

app.mount("/", StaticFiles(directory=static_path, html=True), name="static")


@app.get("/{full_path:path}")
async def catch_all(_full_path: str):
    return FileResponse(os.path.join(static_path, "index.html"))
