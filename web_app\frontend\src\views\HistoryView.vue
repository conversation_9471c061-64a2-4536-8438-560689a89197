<template>
  <div class="history-view">
    <div class="history-header">
      <h3 class="history-title">
        <el-icon><Clock /></el-icon>
        仿真历史
      </h3>
      <el-button
        @click="refreshHistory"
        :loading="loading"
        size="small"
        class="refresh-btn"
      >
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <el-table
      :data="simulations"
      class="history-table"
      @row-click="selectSimulation"
      highlight-current-row
      :current-row-key="selectedRowId"
      row-key="id"
      empty-text="暂无仿真记录"
      v-loading="loading"
      element-loading-text="加载中..."
    >
      <el-table-column prop="id" label="ID" width="80" align="center">
        <template #default="scope">
          <span class="simulation-id">#{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="90" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="small">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="140">
        <template #default="scope">
          <span class="create-time">{{ formatDate(scope.row.created_at) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
           <el-button
             v-if="scope.row.status === 'running'"
             @click.stop="reconnect(scope.row.id)"
             type="primary"
             size="small"
             class="reconnect-btn"
           >
             重连
           </el-button>
           <el-popconfirm
             v-if="scope.row.status !== 'running'"
             title="确定要删除这个仿真记录吗？"
             confirm-button-text="删除"
             cancel-button-text="取消"
             @confirm="deleteSimulation(scope.row.id)"
           >
             <template #reference>
               <el-button
                 @click.stop
                 type="danger"
                 size="small"
                 :icon="Delete"
                 class="delete-btn"
               >
               </el-button>
             </template>
           </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { Clock, Refresh, Delete } from '@element-plus/icons-vue';
import apiClient from '../api/client';
import { ElNotification } from 'element-plus';

export default {
  name: 'HistoryView',
  components: {
    Clock,
    Refresh,
    Delete,
  },
  emits: ['select-simulation', 'reconnect'],
  setup(_props, { emit }) {
    const simulations = ref([]);
    const loading = ref(false);
    const selectedRowId = ref(null);
    const fetchSimulations = async () => {
      loading.value = true;
      try {
        const response = await apiClient.getSimulations();
        simulations.value = response.data;
      } catch (error) {
        console.error('Failed to fetch simulations:', error);
        ElNotification({
          title: '加载失败',
          message: '无法加载仿真历史记录',
          type: 'error',
        });
      } finally {
        loading.value = false;
      }
    };

    const refreshHistory = () => {
      fetchSimulations();
    };

    const addSimulation = (newSimulation) => {
       const index = simulations.value.findIndex(s => s.id === newSimulation.id);
       if (index !== -1) {
           simulations.value[index] = newSimulation;
       } else {
           simulations.value.unshift(newSimulation);
       }
    };

    const selectSimulation = (row) => {
      selectedRowId.value = row.id;
      emit('select-simulation', row.id);
    };

    const reconnect = (id) => {
      emit('reconnect', id);
    };

    const deleteSimulation = async (id) => {
      try {
        await apiClient.deleteSimulation(id);
        await fetchSimulations();
        ElNotification({
          title: '删除成功',
          message: '仿真记录已删除',
          type: 'success',
        });
      } catch (error) {
        console.error('Failed to delete simulation:', error);
        ElNotification({
          title: '删除失败',
          message: '无法删除仿真记录',
          type: 'error',
        });
      }
    };

    const getStatusType = (status) => {
      const statusMap = {
        'running': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'pending': 'info'
      };
      return statusMap[status] || 'info';
    };

    const getStatusText = (status) => {
      const statusMap = {
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'pending': '等待中'
      };
      return statusMap[status] || status;
    };

    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };



    onMounted(() => {
      fetchSimulations();
    });

    return {
      simulations,
      loading,
      selectedRowId,
      selectSimulation,
      deleteSimulation,
      refreshHistory,
      reconnect,
      getStatusType,
      getStatusText,
      formatDate,
      Delete,
      addSimulation,
    };
  },
};
</script>

<style scoped>
/* 传统工业风格历史记录样式 */
.history-view {
  margin-top: 25px;
  background: rgba(236, 240, 241, 0.95);
  border-radius: 6px;
  padding: 20px;
  border: 2px solid #95a5a6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 3px solid #e67e22;
}

.history-title {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #e67e22;
}

.refresh-btn {
  background: #ecf0f1 !important;
  border: 2px solid #95a5a6 !important;
  color: #2c3e50 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.refresh-btn:hover {
  background: #e67e22 !important;
  border-color: #e67e22 !important;
  color: #ffffff !important;
}

/* 传统工业风格表格样式 */
.history-table {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-table :deep(.el-table) {
  background: #ffffff !important;
  color: #2c3e50 !important;
}

.history-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
}

.history-table :deep(.el-table__header th) {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
  color: #e67e22 !important;
  border-bottom: 3px solid #e67e22 !important;
  font-weight: 700 !important;
  padding: 15px 12px !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 13px !important;
}

.history-table :deep(.el-table__body tr) {
  background: #ffffff !important;
  transition: all 0.3s ease;
}

.history-table :deep(.el-table__body tr:hover) {
  background: rgba(230, 126, 34, 0.1) !important;
  cursor: pointer;
}

.history-table :deep(.el-table__body tr.current-row) {
  background: rgba(230, 126, 34, 0.15) !important;
  box-shadow: inset 0 0 0 2px #e67e22;
}

.history-table :deep(.el-table__body td) {
  background: transparent !important;
  border-bottom: 1px solid #bdc3c7 !important;
  color: #2c3e50 !important;
  padding: 12px !important;
  font-weight: 500;
}

.simulation-id {
  color: #27ae60;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.create-time {
  color: #7f8c8d;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.delete-btn {
  background: #ecf0f1 !important;
  border: 2px solid #e74c3c !important;
  color: #e74c3c !important;
  padding: 6px 10px !important;
  min-width: 36px !important;
  border-radius: 4px !important;
  font-weight: 600 !important;
}

.delete-btn:hover {
  background: #e74c3c !important;
  border-color: #e74c3c !important;
  color: #ffffff !important;
  transform: scale(1.05);
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.history-table :deep(.el-table__empty-text) {
  color: #7f8c8d !important;
  font-style: italic;
  font-weight: 500;
}

/* 传统工业风格标签样式 */
.history-table :deep(.el-tag) {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-width: 2px;
}

.history-table :deep(.el-tag--success) {
  background: rgba(39, 174, 96, 0.1);
  border-color: #27ae60;
  color: #27ae60;
}

.history-table :deep(.el-tag--warning) {
  background: rgba(243, 156, 18, 0.1);
  border-color: #f39c12;
  color: #f39c12;
}

.history-table :deep(.el-tag--danger) {
  background: rgba(231, 76, 60, 0.1);
  border-color: #e74c3c;
  color: #e74c3c;
}

.history-table :deep(.el-tag--info) {
  background: rgba(52, 73, 94, 0.1);
  border-color: #34495e;
  color: #34495e;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .history-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .history-table :deep(.el-table__header th),
  .history-table :deep(.el-table__body td) {
    padding: 8px 6px !important;
    font-size: 11px !important;
  }

  .simulation-id {
    font-size: 12px;
  }

  .create-time {
    font-size: 10px;
  }
}

/* Electron应用历史记录适配样式 */
.electron-app .history-view {
  margin-top: 15px;
  padding: 15px;
}

.electron-app .history-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
}

.electron-app .history-title {
  font-size: 14px;
}

.electron-app .refresh-btn {
  font-size: 11px !important;
  padding: 4px 8px !important;
}

.electron-app .history-table :deep(.el-table__header th) {
  padding: 8px 6px !important;
  font-size: 11px !important;
}

.electron-app .history-table :deep(.el-table__body td) {
  padding: 8px 6px !important;
  font-size: 11px !important;
}

.electron-app .simulation-id {
  font-size: 11px;
}

.electron-app .create-time {
  font-size: 10px;
}

.electron-app .delete-btn {
  padding: 4px 6px !important;
  min-width: 28px !important;
  font-size: 11px !important;
}

.electron-app .history-table :deep(.el-tag) {
  font-size: 10px;
  padding: 2px 4px;
}
</style>
