import logging
import json
from app.services.simulation_manager import simulation_manager


class Queue<PERSON>og<PERSON>andler(logging.Handler):
    def __init__(self, simulation_id: int):
        super().__init__()
        self.simulation_id = simulation_id
        self.queue = simulation_manager.queues.get(simulation_id)

    def emit(self, record):
        log_entry = {
            "level": record.levelname,
            "message": record.getMessage(),
        }
        if self.queue:
            self.queue.put_nowait(json.dumps(log_entry))


def get_simulation_logger(simulation_id: int):
    logger = logging.getLogger(f"simulation_{simulation_id}")

    # Clear existing handlers to avoid duplicate logs
    if logger.hasHandlers():
        logger.handlers.clear()

    logger.setLevel(logging.INFO)
    handler = QueueLogHandler(simulation_id)
    # No need for a formatter anymore, as we're creating a dictionary
    logger.addHandler(handler)

    return logger
