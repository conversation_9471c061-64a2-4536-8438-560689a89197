a<template>
  <div class="log-viewer" ref="logContainer">
    <div class="log-header">
      <div>
        <el-icon><Document /></el-icon>
        <span>实时日志</span>
      </div>
    </div>
    <div class="log-content">
      <div v-for="(log, index) in parsedLogs" :key="index" :class="['log-entry', log.level ? `log-${log.level.toLowerCase()}` : '']">
        <span class="log-message">{{ log.message }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch, nextTick, computed } from 'vue';
import { Document } from '@element-plus/icons-vue';

export default {
  name: 'LogViewer',
  components: {
    Document,
  },
  props: {
    logs: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const logContainer = ref(null);

    const parsedLogs = computed(() => {
      return props.logs.map(log => {
        try {
          const parsed = JSON.parse(log);
          if (typeof parsed === 'object' && parsed !== null && 'message' in parsed) {
            return {
              ...parsed,
              level: parsed.level || 'INFO',
            };
          }
        } catch (e) {
          // Not a JSON string, treat as a plain message
        }
        // Handle plain strings or non-standard JSON
        if (typeof log === 'string') {
            if (log.startsWith('status:')) {
                return { message: log, level: 'STATUS' };
            }
        }
        return { message: log, level: 'INFO' };
      });
    });

    watch(
      () => props.logs,
      () => {
        nextTick(() => {
          if (logContainer.value) {
            const content = logContainer.value.querySelector('.log-content');
            content.scrollTop = content.scrollHeight;
          }
        });
      },
      { deep: true }
    );

    return {
      logContainer,
      parsedLogs,
    };
  },
};
</script>

<style scoped>
.log-viewer {
  border: 2px solid #95a5a6;
  border-radius: 6px;
  background-color: #ecf0f1;
  height: 300px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.log-header {
  padding: 12px 15px;
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  color: #e67e22;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 3px solid #e67e22;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.log-header .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.log-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #2c3e50;
  background: #ffffff;
}

.log-entry {
  white-space: pre-wrap;
  border-bottom: 1px solid #ecf0f1;
  padding-bottom: 5px;
  margin-bottom: 5px;
}

.log-info {
  color: #2c3e50;
}

.log-error {
  color: #e74c3c;
  font-weight: bold;
}

.log-warning {
  color: #f39c12;
}

.log-status {
    color: #3498db;
    font-style: italic;
}

.log-content::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb {
  background: rgba(230, 126, 34, 0.6);
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: rgba(230, 126, 34, 0.8);
}
</style>
