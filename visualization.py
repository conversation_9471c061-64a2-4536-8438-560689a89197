#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块
包含静态图表绘制、结果分析图表等功能
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import pandas as pd

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class Visualizer:
    """可视化器类"""

    def __init__(self):
        """初始化可视化器"""
        pass

    def plot_simulation_results(self, simulation_data, save_path=None):
        """
        创建静态结果图（完全按照原版布局）
        """
        # 从仿真数据中提取必要信息
        sim = simulation_data.get("sim_object")  # 需要传入仿真对象
        if sim is None:
            print("错误：需要传入仿真对象")
            return

        fig = plt.figure(figsize=(18, 12))

        # 图1：整体运动轨迹 (占据左侧两行)
        ax1 = plt.subplot(2, 3, (1, 4))
        ax1.set_aspect("equal")
        ax1.grid(True, alpha=0.3)
        ax1.set_title(
            f"六边形卷针旋转及薄膜包覆路径\n旋转中心X偏移: {sim.rotation_center_x_offset:.1f} mm",
            fontsize=14,
            fontweight="bold",
        )
        ax1.set_xlabel("X轴")
        ax1.set_ylabel("Y轴")

        # 绘制过辊A圆心和圆
        ax1.plot(
            sim.A[0],
            sim.A[1],
            "bo",
            markersize=12,
            markerfacecolor="blue",
            markeredgecolor="darkblue",
            markeredgewidth=2,
        )
        ax1.text(
            sim.A[0] + 2,
            sim.A[1],
            "过辊A",
            fontsize=12,
            color="blue",
            fontweight="bold",
        )

        # 绘制过辊A圆
        from matplotlib.patches import Circle, Polygon

        roller_circle_A = Circle(
            (sim.A[0], sim.A[1]),
            sim.roller_radius,
            fill=False,
            color="blue",
            linewidth=1.5,
            alpha=0.7,
        )
        ax1.add_patch(roller_circle_A)

        # 绘制过辊B圆心和圆
        ax1.plot(
            sim.B[0],
            sim.B[1],
            "ro",
            markersize=12,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
        )
        ax1.text(
            sim.B[0] - 8,
            sim.B[1],
            "过辊B",
            fontsize=12,
            color="red",
            fontweight="bold",
        )

        # 绘制过辊B圆
        roller_circle_B = Circle(
            (sim.B[0], sim.B[1]),
            sim.roller_radius,
            fill=False,
            color="red",
            linewidth=1.5,
            alpha=0.7,
        )
        ax1.add_patch(roller_circle_B)

        # 绘制薄膜走带路径（从过辊B到过辊A）
        B_exit = np.array([-30.0, 82.0])
        A_entry = np.array([0.5, 82.0])
        ax1.plot(
            [B_exit[0], A_entry[0]],
            [B_exit[1], A_entry[1]],
            "k-",
            linewidth=2,
            alpha=0.8,
            label="薄膜走带",
        )
        ax1.plot(B_exit[0], B_exit[1], "ko", markersize=6)
        ax1.plot(A_entry[0], A_entry[1], "ko", markersize=6)

        # 绘制几何中心和旋转中心
        ax1.plot(
            sim.geometric_center[0],
            sim.geometric_center[1],
            "gs",
            markersize=10,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            label="几何中心",
        )
        ax1.plot(
            sim.rotation_center[0],
            sim.rotation_center[1],
            "r^",
            markersize=10,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            label="旋转中心",
        )

        # 绘制偏移线
        ax1.plot(
            [sim.geometric_center[0], sim.rotation_center[0]],
            [sim.geometric_center[1], sim.rotation_center[1]],
            "k--",
            alpha=0.7,
            linewidth=2,
            label=f"X偏移: {sim.rotation_center_x_offset:.1f}mm",
        )

        # 绘制初始六边形
        if sim.use_rounded_corners and sim.rounded_corner_calc:
            # 绘制前，重置圆角计算器以反映无厚度的状态
            sim.rounded_corner_calc.apply_thickness_impact(0.0)
            # 圆角模式：绘制带圆角的初始六边形（未旋转）
            self._draw_rounded_hexagon_simple(
                ax1,
                sim.original_vertices,
                sim.rounded_corner_calc,
                rotation_center=None,
                theta=0,
                color="darkblue",
                linewidth=2.5,
                alpha=0.8,
            )
        else:
            # 尖角模式：绘制普通多边形
            hex_patch = Polygon(
                sim.vertices, fill=False, edgecolor="darkblue", linewidth=2.5, alpha=0.8
            )
            ax1.add_patch(hex_patch)

        # 绘制几个关键位置的六边形
        angles_to_show = [0, 60, 120, 180, 240, 300]  # 度
        colors = ["blue", "green", "orange", "red", "purple", "brown"]

        for angle, color in zip(angles_to_show, colors):
            idx = int(angle / sim.step_angle)
            if idx < len(sim.theta):
                theta = sim.theta[idx]

                # 获取该角度的厚度
                thickness_at_angle = sim.accumulated_thickness[idx]

                # 根据厚度获取正确的顶点
                if thickness_at_angle > 0 and sim.use_rounded_corners:
                    # 重要：应用当前快照的厚度
                    current_vertices, _ = (
                        sim.rounded_corner_calc.apply_thickness_impact(
                            thickness_at_angle
                        )
                    )
                else:
                    current_vertices = sim.original_vertices

                # 旋转更新后的顶点
                rotated_vertices = (
                    sim.rotate_vertices_around_center_with_custom_vertices(
                        theta, current_vertices
                    )
                )

                if sim.use_rounded_corners and sim.rounded_corner_calc:
                    # 圆角模式：绘制带圆角的六边形（已旋转）
                    # 此处 rounded_corner_calc 已经有了正确的厚度状态
                    self._draw_rounded_hexagon_simple(
                        ax1,
                        rotated_vertices,
                        sim.rounded_corner_calc,
                        rotation_center=sim.rotation_center,
                        theta=theta,
                        color=color,
                        linewidth=1.5,
                        alpha=0.6,
                        linestyle="--",
                    )
                else:
                    # 尖角模式：绘制普通多边形
                    hex_patch = Polygon(
                        rotated_vertices,
                        fill=False,
                        edgecolor=color,
                        linewidth=1.5,
                        alpha=0.6,
                        linestyle="--",
                    )
                    ax1.add_patch(hex_patch)

                # 绘制薄膜路径（通过过辊接触点）
                if idx < len(sim.roller_contact_points):
                    roller_contact = sim.roller_contact_points[idx]
                    contact = sim.contact_points[idx]

                    # 从接触点到过辊接触点的切线
                    ax1.plot(
                        [contact[0], roller_contact[0]],
                        [contact[1], roller_contact[1]],
                        color=color,
                        linewidth=1.5,
                        alpha=0.7,
                        linestyle="--",
                    )

                    # 标记过辊接触点
                    ax1.plot(
                        roller_contact[0],
                        roller_contact[1],
                        "o",
                        color=color,
                        markersize=4,
                        alpha=0.8,
                    )

        # 绘制接触点轨迹
        ax1.plot(
            sim.contact_points[::50, 0],
            sim.contact_points[::50, 1],
            "r.",
            markersize=2,
            alpha=0.5,
            label="接触点轨迹",
        )

        # 绘制特殊点位轨迹
        ax1.plot(
            sim.upper_point_trajectory[::50, 0],
            sim.upper_point_trajectory[::50, 1],
            "m.",
            markersize=2,
            alpha=0.7,
            label="上方点轨迹",
        )
        ax1.plot(
            sim.lower_point_trajectory[::50, 0],
            sim.lower_point_trajectory[::50, 1],
            "c.",
            markersize=2,
            alpha=0.7,
            label="下方点轨迹",
        )

        # 绘制初始状态的A点到上方点连线
        ax1.plot(
            [sim.A[0], sim.upper_point[0]],
            [sim.A[1], sim.upper_point[1]],
            "m-",
            linewidth=3,
            alpha=0.8,
            label="初始连接：A-上方点",
        )

        # 标注特殊点位的初始和最终位置
        ax1.plot(
            sim.upper_point[0],
            sim.upper_point[1],
            "mo",
            markersize=10,
            markerfacecolor="magenta",
            markeredgecolor="darkmagenta",
            markeredgewidth=2,
        )
        ax1.text(
            sim.upper_point[0] + 2,
            sim.upper_point[1],
            "上方点初始",
            fontsize=10,
            color="magenta",
            fontweight="bold",
        )

        ax1.plot(
            sim.lower_point[0],
            sim.lower_point[1],
            "co",
            markersize=10,
            markerfacecolor="cyan",
            markeredgecolor="darkcyan",
            markeredgewidth=2,
        )
        ax1.text(
            sim.lower_point[0] + 2,
            sim.lower_point[1],
            "下方点初始",
            fontsize=10,
            color="cyan",
            fontweight="bold",
        )

        ax1.set_xlim(-60, 60)
        ax1.set_ylim(-20, 100)
        ax1.legend()

        # 图2：长度变化曲线
        ax2 = plt.subplot(2, 3, 2)
        ax2.grid(True, alpha=0.3)
        ax2.set_title("过辊到卷针长度变化", fontsize=12, fontweight="bold")
        ax2.plot(sim.theta_deg, sim.L, "b-", linewidth=2, label="薄膜长度 L(θ)")
        ax2.set_xlabel("旋转角度 (度)")
        ax2.set_ylabel("薄膜长度 L(θ)")
        ax2.set_xlim(0, sim.total_rotation)
        ax2.legend()

        # 添加统计信息
        max_L = np.max(sim.L)
        min_L = np.min(sim.L)
        mean_L = np.mean(sim.L)
        ax2.text(
            0.02,
            0.98,
            f"最大长度: {max_L:.2f}\n最小长度: {min_L:.2f}\n平均长度: {mean_L:.2f}",
            transform=ax2.transAxes,
            verticalalignment="top",
            fontsize=10,
            bbox=dict(boxstyle="round", facecolor="wheat", alpha=0.8),
        )

        # 图3：薄膜长度变化（切线+包覆）
        ax3 = plt.subplot(2, 3, 3)
        ax3.grid(True, alpha=0.3)
        ax3.set_title(
            "卷针包覆长度分析\n(累积包覆 vs 当前切线)", fontsize=12, fontweight="bold"
        )

        # 绘制累积包覆长度（主要曲线，类似main_coordinate_tangent_analysis.py）
        ax3.plot(sim.theta_deg, sim.S, "b-", linewidth=2, label="累积包覆长度")

        # 绘制当前切线长度
        ax3.plot(
            sim.theta_deg,
            sim.roller_contact_distances,
            "r-",
            linewidth=1.5,
            alpha=0.8,
            label="当前切线长度",
        )

        # 标注关键角度点（删除前面的虚线标记，只保留后面重要的）
        key_angles = []  # 只标注1.5圈和2圈的关键点
        # key_angles = [540, 720]  # 只标注1.5圈和2圈的关键点
        for angle in key_angles:
            if angle <= sim.total_rotation:
                # 找到最接近的索引
                idx = int(angle / sim.step_angle) if sim.step_angle > 0 else 0
                if idx < len(sim.S):
                    ax3.axvline(x=angle, color="red", linestyle="--", alpha=0.5)
                    ax3.text(
                        angle,
                        sim.S[idx],
                        f"{angle}°\n{sim.S[idx]:.1f}mm",
                        ha="center",
                        va="bottom",
                        fontsize=8,
                        bbox=dict(
                            boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7
                        ),
                    )

        # 绘制总薄膜长度（较细的线）
        ax3.plot(
            sim.theta_deg,
            sim.S_total,
            "purple",
            linewidth=1,
            alpha=0.6,
            label="总薄膜长度",
        )

        # 计算从0开始的总长度曲线（减去第一个点的长度）
        S_total_from_zero = sim.S_total - sim.S_total[0]
        ax3.plot(
            sim.theta_deg,
            S_total_from_zero,
            "gray",
            linewidth=1,
            linestyle="--",
            alpha=0.6,
            label="总长度(从0开始)",
        )

        ax3.set_xlabel("旋转角度 (度)")
        ax3.set_ylabel("薄膜长度")
        ax3.set_xlim(0, sim.total_rotation)

        # 添加总长度标注
        total_S_total = sim.S_total[-1]
        total_tangent = sim.roller_contact_distances[-1]
        total_surface = sim.S[-1]
        total_from_zero = S_total_from_zero[-1]

        ax3.text(
            sim.total_rotation / 2,
            total_S_total * 0.9,
            f"总长度: {total_S_total:.2f}\n从0开始: {total_from_zero:.2f}\n切线: {total_tangent:.2f}\n包覆: {total_surface:.2f}",
            fontsize=9,
            ha="center",
            fontweight="bold",
            bbox=dict(boxstyle="round", facecolor="lightcoral", alpha=0.8),
        )
        ax3.legend()

        # 图4：隔膜厚度分析
        ax4 = plt.subplot(2, 3, 5)
        ax4.grid(True, alpha=0.3)
        ax4.set_title("隔膜厚度与层数分析", fontsize=12, fontweight="bold")

        # 左Y轴：累积厚度
        ax4_twin = ax4.twinx()

        # 绘制累积厚度
        line1 = ax4.plot(
            sim.theta_deg,
            sim.accumulated_thickness,
            "b-",
            linewidth=2,
            label=f"累积厚度 (厚度={sim.film_thickness:.2f}mm/层)",
        )
        ax4.set_xlabel("旋转角度 (度)")
        ax4.set_ylabel("累积厚度 (mm)", color="b")
        ax4.tick_params(axis="y", labelcolor="b")
        ax4.set_xlim(0, sim.total_rotation)

        # 右Y轴：层数
        line2 = ax4_twin.plot(
            sim.theta_deg, sim.layer_numbers, "r-", linewidth=2, label="层数"
        )
        ax4_twin.set_ylabel("层数", color="r")
        ax4_twin.tick_params(axis="y", labelcolor="r")

        # 合并图例
        lines = line1 + line2
        labels = [line.get_label() for line in lines]
        ax4.legend(lines, labels, loc="upper left")

        # 添加统计信息
        max_layers = np.max(sim.layer_numbers)
        max_thickness = np.max(sim.accumulated_thickness)
        total_consumption = np.sum(sim.layer_consumption)

        info_text = f"最大层数: {max_layers}\n最大厚度: {max_thickness:.2f}mm\n总消耗: {total_consumption:.1f}mm"
        ax4.text(
            0.02,
            0.98,
            info_text,
            transform=ax4.transAxes,
            verticalalignment="top",
            fontsize=10,
            bbox=dict(boxstyle="round", facecolor="lightblue", alpha=0.8),
        )

        # 图5：隔膜消耗长度分析
        ax5 = plt.subplot(2, 3, 6)
        ax5.grid(True, alpha=0.3)
        ax5.set_title("每层隔膜消耗长度", fontsize=12, fontweight="bold")

        # 只显示有消耗的层数
        non_zero_indices = sim.layer_consumption > 0
        if np.any(non_zero_indices):
            ax5.plot(
                sim.theta_deg[non_zero_indices],
                sim.layer_consumption[non_zero_indices],
                "g-",
                linewidth=2,
                label="每层消耗长度",
            )
            ax5.set_xlabel("旋转角度 (度)")
            ax5.set_ylabel("消耗长度 (mm)")
            ax5.set_xlim(0, sim.total_rotation)
            ax5.legend()

            # 添加总消耗标注
            ax5.text(
                0.02,
                0.98,
                f"总消耗: {total_consumption:.1f}mm",
                transform=ax5.transAxes,
                verticalalignment="top",
                fontsize=10,
                bbox=dict(boxstyle="round", facecolor="lightgreen", alpha=0.8),
            )

        plt.tight_layout()

        if save_path:
            print("保存结果图片为PNG文件...")
            fig.savefig(save_path)
            print("PNG保存完成!")

        plt.show()

    def plot_contact_analysis(self, simulation_data, save_path=None):
        """
        绘制接触点分析图表

        Parameters:
        simulation_data: dict - 仿真数据
        save_path: str - 保存路径（可选）
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        theta_deg = simulation_data["theta_deg"]
        contact_points = simulation_data["contact_points"]

        # 1. 接触点轨迹
        ax1.plot(
            contact_points[:, 0], contact_points[:, 1], "b-", linewidth=1, alpha=0.7
        )
        ax1.scatter(
            contact_points[::50, 0], contact_points[::50, 1], c="red", s=20, alpha=0.8
        )
        ax1.set_xlabel("X坐标 (mm)")
        ax1.set_ylabel("Y坐标 (mm)")
        ax1.set_title("接触点轨迹", fontweight="bold")
        ax1.grid(True, alpha=0.3)
        ax1.set_aspect("equal")

        # 2. 接触点X坐标变化
        ax2.plot(theta_deg, contact_points[:, 0], "g-", linewidth=2)
        ax2.set_xlabel("旋转角度 (度)")
        ax2.set_ylabel("接触点X坐标 (mm)")
        ax2.set_title("接触点X坐标随角度变化", fontweight="bold")
        ax2.grid(True, alpha=0.3)

        # 3. 接触点Y坐标变化
        ax3.plot(theta_deg, contact_points[:, 1], "orange", linewidth=2)
        ax3.set_xlabel("旋转角度 (度)")
        ax3.set_ylabel("接触点Y坐标 (mm)")
        ax3.set_title("接触点Y坐标随角度变化", fontweight="bold")
        ax3.grid(True, alpha=0.3)

        # 4. 接触点类型分布
        contact_types = simulation_data["contact_type"]
        unique_types = list(set(contact_types))
        type_counts = [contact_types.count(t) for t in unique_types]

        ax4.pie(type_counts, labels=unique_types, autopct="%1.1f%%", startangle=90)
        ax4.set_title("接触点类型分布", fontweight="bold")

        plt.suptitle("接触点分析", fontsize=16, fontweight="bold")
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        plt.show()

    def plot_thickness_analysis(self, simulation_data, save_path=None):
        """
        绘制厚度影响分析图表

        Parameters:
        simulation_data: dict - 仿真数据
        save_path: str - 保存路径（可选）
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        theta_deg = simulation_data["theta_deg"]

        # 1. 厚度累积过程
        ax1.plot(theta_deg, simulation_data["accumulated_thickness"], "b-", linewidth=2)
        ax1.set_xlabel("旋转角度 (度)")
        ax1.set_ylabel("累积厚度 (mm)")
        ax1.set_title("厚度累积过程", fontweight="bold")
        ax1.grid(True, alpha=0.3)

        # 2. 每层消耗长度变化
        ax2.plot(theta_deg, simulation_data["layer_consumption"], "r-", linewidth=2)
        ax2.set_xlabel("旋转角度 (度)")
        ax2.set_ylabel("每层消耗长度 (mm)")
        ax2.set_title("每层消耗长度变化", fontweight="bold")
        ax2.grid(True, alpha=0.3)

        # 3. 层数阶梯图
        ax3.step(
            theta_deg, simulation_data["layer_numbers"], "g-", linewidth=2, where="post"
        )
        ax3.set_xlabel("旋转角度 (度)")
        ax3.set_ylabel("层数")
        ax3.set_title("层数变化（阶梯图）", fontweight="bold")
        ax3.grid(True, alpha=0.3)

        # 4. 厚度影响效率
        base_consumption = simulation_data["layer_consumption"][0]  # 第0层消耗
        thickness_effect = simulation_data["layer_consumption"] - base_consumption
        efficiency = thickness_effect / simulation_data["accumulated_thickness"]
        efficiency[simulation_data["accumulated_thickness"] == 0] = 0  # 避免除零

        ax4.plot(theta_deg, efficiency, "m-", linewidth=2)
        ax4.set_xlabel("旋转角度 (度)")
        ax4.set_ylabel("厚度影响效率 (mm/mm)")
        ax4.set_title("厚度影响效率", fontweight="bold")
        ax4.grid(True, alpha=0.3)

        plt.suptitle("厚度影响分析", fontsize=16, fontweight="bold")
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        plt.show()

    def plot_geometry_snapshot(
        self,
        vertices,
        contact_point,
        roller_A,
        roller_radius,
        angle_deg,
        save_path=None,
        rounded_corner_calc=None,
        rotation_center=None,
    ):
        """
        绘制几何快照

        Parameters:
        vertices: np.array - 当前顶点坐标
        contact_point: np.array - 接触点坐标
        roller_A: np.array - 过辊A位置
        roller_radius: float - 过辊半径
        angle_deg: float - 当前角度
        save_path: str - 保存路径（可选）
        """
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))

        # 绘制六边形
        if rounded_corner_calc is None:
            # 尖角模式：直接连接顶点
            hex_closed = np.vstack([vertices, vertices[0]])
            ax.plot(
                hex_closed[:, 0],
                hex_closed[:, 1],
                "b-",
                linewidth=3,
                label="六边形卷针",
            )
        else:
            # 圆角模式：绘制边和圆弧
            theta = np.deg2rad(angle_deg) if rotation_center is not None else 0
            self._draw_rounded_hexagon_simple(
                ax,
                vertices,
                rounded_corner_calc,
                rotation_center=rotation_center,
                theta=theta,
                color="blue",
                linewidth=3,
                label="六边形卷针(圆角)",
            )

        # 标记顶点
        for i, vertex in enumerate(vertices):
            ax.plot(vertex[0], vertex[1], "bo", markersize=8)
            ax.annotate(
                f"V{i + 1}",
                vertex,
                xytext=(5, 5),
                textcoords="offset points",
                fontsize=10,
                fontweight="bold",
            )

        # 如果有圆角，绘制内切圆
        if rounded_corner_calc is not None:
            self._draw_incircles(ax, rounded_corner_calc)

        # 绘制过辊
        roller_circle = Circle(
            roller_A, roller_radius, fill=False, color="red", linewidth=2
        )
        ax.add_patch(roller_circle)
        ax.plot(roller_A[0], roller_A[1], "r^", markersize=10, label="过辊A")

        # 绘制接触点
        ax.plot(contact_point[0], contact_point[1], "go", markersize=10, label="接触点")

        # 绘制切线
        ax.plot(
            [contact_point[0], roller_A[0]],
            [contact_point[1], roller_A[1]],
            "g--",
            linewidth=2,
            alpha=0.7,
            label="切线",
        )

        ax.set_xlabel("X坐标 (mm)")
        ax.set_ylabel("Y坐标 (mm)")
        ax.set_title(f"几何快照 - 角度: {angle_deg:.1f}°", fontweight="bold")
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_aspect("equal")

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        plt.show()

    def _draw_rounded_hexagon(self, ax, vertices, rounded_corner_calc):
        """绘制带圆角的六边形"""
        n = len(vertices)

        # 预计算所有切点
        all_tangent_points = []
        for i in range(n):
            corner_info = rounded_corner_calc.get_rounded_corner_info(i)
            radius = corner_info["radius"]
            center = corner_info["center"]

            # 计算当前顶点的两个切点
            tangent_point1, tangent_point2 = (
                rounded_corner_calc.calculate_tangent_points_on_edges(
                    center, radius, i, vertices
                )
            )
            all_tangent_points.append((tangent_point1, tangent_point2))

        # 绘制所有边和圆弧
        for i in range(n):
            current_tangent1, current_tangent2 = all_tangent_points[i]
            next_tangent1, _ = all_tangent_points[(i + 1) % n]

            # 绘制从当前顶点的第二个切点到下一个顶点的第一个切点的边
            ax.plot(
                [current_tangent2[0], next_tangent1[0]],
                [current_tangent2[1], next_tangent1[1]],
                "b-",
                linewidth=2,
                alpha=0.8,
            )

            # 绘制当前顶点的圆弧
            corner_info = rounded_corner_calc.get_rounded_corner_info(i)
            center = corner_info["center"]
            radius = corner_info["radius"]

            self._draw_arc(
                ax,
                center,
                radius,
                current_tangent1,
                current_tangent2,
                "b-",
                linewidth=2,
                alpha=0.8,
            )

        # 添加标签
        ax.plot([], [], "b-", linewidth=2, label="六边形卷针(圆角)")

    def _draw_rounded_hexagon_simple(
        self, ax, vertices, rounded_corner_calc, rotation_center=None, theta=0, **kwargs
    ):
        """
        简化的圆角六边形绘制（用于主结果图）

        Parameters:
        ax: matplotlib axis
        vertices: np.array - 顶点坐标（可能已旋转）
        rounded_corner_calc: RoundedCornerCalculator - 圆角计算器
        rotation_center: np.array - 旋转中心（如果顶点已旋转）
        theta: float - 旋转角度（弧度，如果顶点已旋转）
        **kwargs: 绘制参数
        """
        n = len(vertices)

        # 获取圆心坐标（如果顶点已旋转，圆心也需要旋转）
        if rotation_center is not None and theta != 0:
            # 顶点已旋转，需要旋转圆心
            rotated_centers = rounded_corner_calc.get_rotated_centers(
                rotation_center, theta
            )
        else:
            # 顶点未旋转，使用原始圆心
            rotated_centers = rounded_corner_calc.circle_centers

        # 预计算所有切点
        all_tangent_points = []
        for i in range(n):
            corner_info = rounded_corner_calc.get_rounded_corner_info(i)
            radius = corner_info["radius"]
            center = rotated_centers[i]  # 使用正确的圆心

            # 计算当前顶点的两个切点
            tangent_point1, tangent_point2 = (
                rounded_corner_calc.calculate_tangent_points_on_edges_rotated(
                    center, radius, i, vertices
                )
            )
            all_tangent_points.append((tangent_point1, tangent_point2))

        # 绘制所有边和圆弧
        for i in range(n):
            current_tangent1, current_tangent2 = all_tangent_points[i]
            next_tangent1, _ = all_tangent_points[(i + 1) % n]

            # 绘制从当前顶点的第二个切点到下一个顶点的第一个切点的边
            ax.plot(
                [current_tangent2[0], next_tangent1[0]],
                [current_tangent2[1], next_tangent1[1]],
                **kwargs,
            )

            # 绘制当前顶点的圆弧
            center = rotated_centers[i]  # 使用正确的圆心
            radius = rounded_corner_calc.get_rounded_corner_info(i)["radius"]

            self._draw_arc(
                ax, center, radius, current_tangent1, current_tangent2, **kwargs
            )

    def _draw_incircles(self, ax, rounded_corner_calc):
        """绘制内切圆"""
        for i, center in enumerate(rounded_corner_calc.circle_centers):
            corner_info = rounded_corner_calc.get_rounded_corner_info(i)
            radius = corner_info["radius"]
            is_sharp = corner_info["is_sharp"]

            color = "orange" if is_sharp else "purple"
            alpha = 0.3

            circle = Circle(
                center,
                radius,
                fill=False,
                color=color,
                linewidth=1,
                alpha=alpha,
                linestyle="--",
            )
            ax.add_patch(circle)

            # 标记圆心
            ax.plot(center[0], center[1], "o", color=color, markersize=4, alpha=0.7)

    def _draw_arc(self, ax, center, radius, start_point, end_point, *args, **kwargs):
        """绘制圆弧"""
        # 计算起始和结束角度
        start_angle = np.arctan2(start_point[1] - center[1], start_point[0] - center[0])
        end_angle = np.arctan2(end_point[1] - center[1], end_point[0] - center[0])

        # 确保角度在[0, 2π]范围内
        start_angle = start_angle % (2 * np.pi)
        end_angle = end_angle % (2 * np.pi)

        # 确定绘制方向（逆时针）
        if end_angle < start_angle:
            end_angle += 2 * np.pi

        # 限制角度差，避免绘制过长的弧
        angle_diff = end_angle - start_angle
        if angle_diff > np.pi:
            # 如果角度差大于π，选择较短的弧
            start_angle, end_angle = end_angle - 2 * np.pi, start_angle
            angle_diff = end_angle - start_angle

        # 生成圆弧点
        num_points = max(10, int(abs(angle_diff) * 30 / np.pi))  # 根据角度调整点数
        angles = np.linspace(start_angle, end_angle, num_points)

        arc_x = center[0] + radius * np.cos(angles)
        arc_y = center[1] + radius * np.sin(angles)

        ax.plot(arc_x, arc_y, *args, **kwargs)

    def export_data_to_excel(self, simulation_data, file_path):
        """
        导出仿真数据到Excel文件

        Parameters:
        simulation_data: dict - 仿真数据
        file_path: str - Excel文件路径
        """
        # 创建DataFrame
        df = pd.DataFrame(
            {
                "角度(度)": simulation_data["theta_deg"],
                "包覆长度(mm)": simulation_data["L"],
                "增量消耗(mm)": simulation_data["S"],
                "累积消耗(mm)": simulation_data["S_total"],
                "层数": simulation_data["layer_numbers"],
                "累积厚度(mm)": simulation_data["accumulated_thickness"],
                "每层消耗(mm)": simulation_data["layer_consumption"],
                "接触点X(mm)": simulation_data["contact_points"][:, 0],
                "接触点Y(mm)": simulation_data["contact_points"][:, 1],
                "接触点类型": simulation_data["contact_type"],
            }
        )

        # 保存到Excel
        with pd.ExcelWriter(file_path, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="仿真数据", index=False)

            # 添加统计信息
            stats_df = pd.DataFrame(
                {
                    "统计项目": [
                        "总旋转角度",
                        "总层数",
                        "最大厚度",
                        "总消耗长度",
                        "平均每层消耗",
                    ],
                    "数值": [
                        simulation_data["theta_deg"][-1],
                        simulation_data["layer_numbers"][-1],
                        simulation_data["accumulated_thickness"][-1],
                        simulation_data["S_total"][-1],
                        np.mean(simulation_data["layer_consumption"]),
                    ],
                    "单位": ["度", "层", "mm", "mm", "mm"],
                }
            )
            stats_df.to_excel(writer, sheet_name="统计信息", index=False)

        print(f"数据已导出到: {file_path}")
