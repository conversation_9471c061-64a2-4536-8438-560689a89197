const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 创建一个WebSocket包装器，使用IPC与主进程通信
class ElectronWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = 0; // CONNECTING
    this.onopen = null;
    this.onmessage = null;
    this.onclose = null;
    this.onerror = null;

    // 生成唯一ID
    this.id = Math.random().toString(36).substring(2, 11);

    // 监听来自主进程的消息
    ipcRenderer.on(`websocket-${this.id}`, (_, type, data) => {
      switch (type) {
        case 'open':
          this.readyState = 1; // OPEN
          if (this.onopen) this.onopen();
          break;
        case 'message':
          if (this.onmessage) this.onmessage({ data });
          break;
        case 'close':
          this.readyState = 3; // CLOSED
          if (this.onclose) this.onclose(data);
          break;
        case 'error':
          if (this.onerror) this.onerror(data);
          break;
      }
    });

    // 请求主进程创建WebSocket连接
    ipcRenderer.send('create-websocket', this.id, url);

  }

  send(data) {
    if (this.readyState === 1) { // OPEN
      ipcRenderer.send('websocket-send', this.id, data);
    }
  }

  close(code, reason) {
    this.readyState = 2; // CLOSING
    ipcRenderer.send('websocket-close', this.id, code, reason);
  }

  // 添加addEventListener支持
  addEventListener(type, listener) {
    if (type === 'open') this.onopen = listener;
    else if (type === 'message') this.onmessage = listener;
    else if (type === 'close') this.onclose = listener;
    else if (type === 'error') this.onerror = listener;
  }

  removeEventListener(type, listener) {
    if (type === 'open' && this.onopen === listener) this.onopen = null;
    else if (type === 'message' && this.onmessage === listener) this.onmessage = null;
    else if (type === 'close' && this.onclose === listener) this.onclose = null;
    else if (type === 'error' && this.onerror === listener) this.onerror = null;
  }
}

// WebSocket状态常量
ElectronWebSocket.CONNECTING = 0;
ElectronWebSocket.OPEN = 1;
ElectronWebSocket.CLOSING = 2;
ElectronWebSocket.CLOSED = 3;

// 确保WebSocket常量在全局可用
if (!window.WebSocket) {
  window.WebSocket = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
  };
}

contextBridge.exposeInMainWorld('electronAPI', {
  createWebSocket: (url) => new ElectronWebSocket(url),
  isElectron: true,
});
