# Electron应用安全配置

## 概述
本文档描述了为解决Electron安全警告而实施的安全措施。

## 安全警告解决方案

### 1. Content Security Policy (CSP)
已在以下位置配置CSP：

#### HTML Meta标签 (index.html)
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: blob:;
  font-src 'self' data:;
  connect-src 'self' http://127.0.0.1:* http://localhost:* ws://127.0.0.1:* ws://localhost:*;
  object-src 'none';
  base-uri 'self';
  form-action 'self';
">
```

#### HTTP响应头 (security.js)
通过Electron的webRequest API动态设置CSP头。

### 2. BrowserWindow安全配置

#### 核心安全设置
```javascript
webPreferences: {
  nodeIntegration: false,        // 禁用Node.js集成
  contextIsolation: true,        // 启用上下文隔离
  enableRemoteModule: false,     // 禁用remote模块
  webSecurity: true,             // 启用Web安全
  allowRunningInsecureContent: false, // 禁止不安全内容
  experimentalFeatures: false,   // 禁用实验性功能
  plugins: false,                // 禁用插件
  webgl: true,                   // 允许WebGL（图表需要）
  webaudio: false               // 禁用Web Audio
}
```

### 3. Preload脚本安全化

#### 使用contextBridge
```javascript
const { contextBridge } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  isElectron: true,
  platform: process.platform,
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  }
});
```

### 4. 网络请求安全控制

#### 阻止外部请求
- 只允许本地服务器 (127.0.0.1, localhost)
- 阻止所有外部HTTP/HTTPS请求
- 记录被阻止的请求

#### 权限管理
- 拒绝所有权限请求（除非明确需要）
- 可根据需要添加特定权限

### 5. 窗口安全措施

#### 窗口创建控制
- 阻止新窗口创建
- 防止恶意弹窗
- 控制导航行为

#### 显示控制
- 窗口初始隐藏
- 等待ready-to-show事件
- 错误处理机制

## 文件结构

### 安全相关文件
- `main.js` - 主进程安全配置
- `preload.js` - 预加载脚本安全API
- `security.js` - 安全配置模块
- `web_app/frontend/index.html` - CSP配置

### 配置模块
- `setupSecurity()` - 应用安全设置
- `getSecureWindowOptions()` - 获取安全窗口配置

## 安全检查清单

### ✅ 已实施的安全措施
- [x] 禁用Node.js集成
- [x] 启用上下文隔离
- [x] 配置Content Security Policy
- [x] 使用contextBridge安全API
- [x] 阻止外部网络请求
- [x] 禁用不安全功能
- [x] 权限请求控制
- [x] 窗口创建控制

### 🔒 安全级别
- **高**: 生产环境推荐配置
- **兼容**: 保持应用功能完整性
- **可维护**: 清晰的配置结构

## 开发vs生产环境

### 开发环境
```javascript
if (process.env.NODE_ENV === 'development') {
  win.webContents.openDevTools();
}
```

### 生产环境
- 自动隐藏开发者工具
- 更严格的CSP策略
- 完整的错误处理

## 常见问题解决

### Q: 仍然看到安全警告？
A: 确保：
1. CSP正确配置
2. 没有使用eval()
3. 所有脚本都是内联或来自同源

### Q: 应用功能受影响？
A: 检查：
1. CSP是否过于严格
2. 必要的权限是否被阻止
3. 网络请求是否被误拦截

### Q: 如何添加新的安全策略？
A: 修改 `security.js` 中的相应配置函数

## 更新和维护

### 定期检查
- Electron版本更新
- 安全补丁应用
- CSP策略审查

### 监控
- 控制台安全警告
- 被阻止的请求日志
- 权限请求记录

## 参考资源
- [Electron Security Guidelines](https://electronjs.org/docs/tutorial/security)
- [Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [Electron Best Practices](https://electronjs.org/docs/tutorial/security#checklist-security-recommendations)
