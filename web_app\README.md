# 六边形卷针隔膜包覆仿真系统

## 🎯 系统概述

这是一个专业的六边形卷针隔膜包覆动态仿真系统，采用现代化的前后端分离架构，提供工业级的用户界面和高精度的仿真计算。

### ✨ 主要特性

- **🎨 工业风格界面**: 采用现代工业设计风格，深色主题，专业美观
- **🔧 完整参数配置**: 支持所有仿真参数的实时调整
- **📊 实时动画显示**: 高质量的动态仿真动画，包含原始卷针和包覆效果
- **🎯 精确仿真计算**: 支持尖角和圆角两种仿真模式
- **📈 数据可视化**: 丰富的图表展示仿真结果
- **💾 历史记录管理**: 自动保存仿真历史，支持结果对比
- **📤 数据导出**: 支持Excel格式的数据导出

### 🏗️ 技术架构

- **前端**: Vue 3 + Element Plus + ECharts
- **后端**: FastAPI + SQLAlchemy + PostgreSQL
- **仿真引擎**: NumPy + 自研算法
- **包管理**: uv (Python) + npm (Node.js)

## 🚀 快速开始

### 环境要求

- Python 3.12+
- Node.js 18+
- uv 包管理器
- npm 包管理器

### 安装依赖

1. **安装 uv 包管理器**:
   ```bash
   pip install uv
   ```

2. **安装 Python 依赖**:
   ```bash
   cd web_app
   uv sync
   ```

3. **安装前端依赖**:
   ```bash
   cd frontend
   npm install
   ```

### 启动开发环境

#### 方式一：一键启动（推荐）
```bash
cd web_app
python start_dev.py
```

#### 方式二：分别启动

**启动后端**:
```bash
cd web_app/backend
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**启动前端**:
```bash
cd web_app/frontend
npm run dev
```

### 访问系统

- **前端界面**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 📋 功能说明

### 仿真参数配置

#### 基础参数
- **卷针形状**: 目前支持六边形
- **旋转中心X偏移**: 控制旋转中心位置 (默认: 2.0mm)
- **隔膜厚度**: 单层隔膜厚度 (默认: 0.1mm)
- **总旋转角度**: 仿真的总角度范围 (默认: 3600°)
- **角度步长**: 计算精度控制 (默认: 0.5°)

#### 圆角设置
- **启用圆角仿真**: 开关圆角计算模式
- **锐角圆角半径**: V1、V4顶点的圆角半径 (默认: 0.8mm)
- **钝角圆角半径**: V2、V3、V5、V6顶点的圆角半径 (默认: 12.0mm)

#### 过辊配置
- **过辊A位置**: (0.5, 80.0) mm
- **过辊B位置**: (-30.0, 80.0) mm
- **过辊半径**: 2.0 mm

#### 卷针顶点坐标
六个顶点的精确坐标配置，支持自定义卷针形状。

### 仿真结果分析

#### 动态动画
- **原始卷针轮廓**: 蓝色虚线显示
- **当前包覆状态**: 红色实线显示
- **薄膜切线**: 绿色线条
- **接触点**: 红色圆点
- **过辊接触点**: 绿色圆点
- **包覆区域**: 渐变色显示

#### 实时信息面板
- 当前旋转角度和层数
- 累积厚度信息
- 长度统计（总长度、切线部分、包覆部分）
- 接触点类型和位置
- 配置参数显示

#### 数据图表
- 包覆距离变化曲线
- 切线长度变化曲线
- 总长度变化曲线
- 厚度累积曲线

### 历史记录管理
- 自动保存每次仿真结果
- 支持历史记录浏览和加载
- 仿真状态跟踪（运行中/已完成/失败）

## 🔧 开发说明

### 项目结构
```
web_app/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── db/             # 数据库
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模式
│   │   ├── services/       # 业务逻辑
│   │   └── simulation/     # 仿真引擎
│   └── main.py
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── api/           # API客户端
│   │   ├── components/    # Vue组件
│   │   └── views/         # 页面视图
│   └── package.json
├── start_dev.py           # 开发环境启动脚本
├── test_integration.py    # 集成测试
└── README.md
```

### 核心优化

1. **包覆距离计算优化**: 修复了90度附近的虚假突变问题
2. **默认参数统一**: 前后端使用一致的默认值
3. **工业风格界面**: 专业的深色主题设计
4. **圆角仿真支持**: 完整的圆角计算逻辑
5. **动画效果增强**: 显示原始卷针和包覆效果

### 测试

运行集成测试：
```bash
cd web_app
uv run python test_integration.py
```

## 📊 使用示例

1. **启动系统**: 运行 `python start_dev.py`
2. **配置参数**: 在左侧面板调整仿真参数
3. **运行仿真**: 点击"开始仿真"按钮
4. **查看结果**: 观察动画和数据图表
5. **导出数据**: 下载Excel格式的仿真数据

## 🐛 故障排除

### 常见问题

1. **端口占用**: 确保8000和5173端口未被占用
2. **依赖缺失**: 运行 `uv sync` 和 `npm install` 重新安装依赖
3. **数据库连接**: 检查PostgreSQL服务是否正常运行

### 日志查看

- 后端日志: 控制台输出
- 前端日志: 浏览器开发者工具
- 仿真日志: 后端控制台

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: 2.0.0
**更新日期**: 2025年1月
**开发团队**: 数码一体机凸轮优化项目组
