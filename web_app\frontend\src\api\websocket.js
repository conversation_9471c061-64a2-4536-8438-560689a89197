class WebSocketService {
  constructor() {
    this.connections = {};
    this.heartbeatIntervals = {};
  }

  connect(simulationId, onMessageCallback) {
    if (this.connections[simulationId]) {
      return;
    }

    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${window.location.host}/ws/${simulationId}`;

    const socket = new WebSocket(wsUrl);
    this.connections[simulationId] = socket;

    socket.onopen = () => {
      console.log(`WebSocket connected for simulation ${simulationId}`);
      // 启动心跳
      this._startHeartbeat(simulationId);
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // 处理心跳响应
        if (data.type === 'pong') {
          console.log(`Received pong for simulation ${simulationId}`);
          return;
        }

        // 处理其他消息
        onMessageCallback(event.data);
      } catch (error) {
        // 如果不是JSON，直接传递原始数据
        onMessageCallback(event.data);
      }
    };

    socket.onclose = (event) => {
      console.log(`WebSocket closed for simulation ${simulationId}`, event);
      this._stopHeartbeat(simulationId);
      delete this.connections[simulationId];
    };

    socket.onerror = (error) => {
      console.error(`WebSocket error for simulation ${simulationId}:`, error);
      this._stopHeartbeat(simulationId);
      delete this.connections[simulationId];
    };
  }

  _startHeartbeat(simulationId) {
    // 每25秒发送一次心跳（服务器超时是30秒）
    this.heartbeatIntervals[simulationId] = setInterval(() => {
      const socket = this.connections[simulationId];
      if (socket && socket.readyState === WebSocket.OPEN) {
        try {
          socket.send(JSON.stringify({
            type: 'ping',
            timestamp: Date.now()
          }));
        } catch (error) {
          console.error(`Failed to send heartbeat for simulation ${simulationId}:`, error);
          this._stopHeartbeat(simulationId);
        }
      } else {
        this._stopHeartbeat(simulationId);
      }
    }, 25000);
  }

  _stopHeartbeat(simulationId) {
    if (this.heartbeatIntervals[simulationId]) {
      clearInterval(this.heartbeatIntervals[simulationId]);
      delete this.heartbeatIntervals[simulationId];
    }
  }

  disconnect(simulationId) {
    if (this.connections[simulationId]) {
      this._stopHeartbeat(simulationId);
      this.connections[simulationId].close();
      delete this.connections[simulationId];
    }
  }

  disconnectAll() {
    Object.keys(this.connections).forEach(simulationId => {
      this.disconnect(simulationId);
    });
  }
}

export const webSocketService = new WebSocketService();
