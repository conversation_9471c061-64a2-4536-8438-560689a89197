# Electron Desktop App Architecture

This document outlines the architecture for the Electron-based desktop application that wraps the existing FastAPI and Vue.js web application.

## 1. Project Directory Structure

The `desktop_app` project will be organized as follows to keep a clear separation between the Electron source code, the packaged backend, and build artifacts.

```
desktop_app/
├── ARCHITECTURE.md         # The architecture document (this file)
├── build/                  # Output for the packaged Electron app (generated by electron-builder)
│   ├── ...
├── dist_backend/           # Output for the packaged Python backend
│   └── backend_executable  # The PyInstaller executable for the FastAPI server
├── node_modules/           # Node.js dependencies
├── src/
│   ├── main/               # Electron main process source code
│   │   ├── index.js        # Main process entry point
│   │   └── preload.js      # Preload script for the browser window
│   └── renderer/           # Electron renderer process source (if any native UI is needed)
│       └── index.html      # A simple loader/splash screen page
├── package.json            # NPM dependencies and build scripts
└── package-lock.json
```

## 2. Backend Packaging Strategy

The FastAPI backend and the Vue.js frontend will be packaged into a single standalone executable using PyInstaller.

**Packaging Flow:**

1.  **Build Frontend**: The Vue.js frontend is built using `npm run build` in the `web_app/frontend` directory. This generates a static `dist` directory.
2.  **Bundle Frontend**: The generated `web_app/frontend/dist` directory is copied into the backend's source tree, for example, to `web_app/backend/app/static`.
3.  **Configure FastAPI**: The FastAPI application is configured to serve the Vue.js application as static files from the `/static` directory and serve `index.html` for the root path.
4.  **Package with PyInstaller**: PyInstaller is used to package the FastAPI application. The PyInstaller `.spec` file will be configured to include the `static` directory as data files, ensuring they are bundled into the final executable. The output will be placed in `desktop_app/dist_backend/`.

```mermaid
graph TD
    A[Vue.js Source] -- npm run build --> B(Frontend 'dist' folder);
    C[FastAPI Source] -- Mounts static files --> D{FastAPI App};
    B -- Copied to --> C;
    D -- Packaged by PyInstaller --> E[backend_executable];
```

## 3. Process Management

The Electron main process (`src/main/index.js`) is responsible for the entire lifecycle of the Python backend process.

**Lifecycle Management:**

-   **Launch**: On Electron app startup (`app.whenReady()`), the main process will launch the `backend_executable` located in the `dist_backend` directory using Node.js's `child_process.spawn()`. The path to the executable will be resolved using `path.join(app.getAppPath(), '..', 'dist_backend', 'backend_executable')` for packaged applications.
-   **Monitoring**: The main process will listen for `stdout`, `stderr`, and `exit` events from the child process to log its status and detect crashes.
-   **Termination**: When the Electron application quits (`app.on('will-quit')`), a `SIGTERM` signal will be sent to the child process to ensure a graceful shutdown of the FastAPI server. If it doesn't terminate, it will be forcefully killed.

```mermaid
sequenceDiagram
    participant E as Electron Main Process
    participant P as Python Backend Process

    E->>E: App starts
    E->>P: spawn('backend_executable', ['--port', free_port])
    P->>E: stdout/stderr (logging)
    Note over E,P: Backend is running

    E->>E: User quits app
    E->>P: kill('SIGTERM')
    P->>P: Graceful shutdown
    P-->>E: 'exit' event
```

## 4. Port Management

To avoid conflicts, the application will dynamically find and assign a free network port for the backend server.

**Mechanism:**

1.  **Find Free Port**: The Electron main process will use a library like `portfinder` to find an available TCP port, starting from a default (e.g., 8000).
2.  **Pass Port to Backend**: The discovered port number is passed as a command-line argument to the `backend_executable` when it is spawned (e.g., `./backend_executable --port 8080`).
3.  **Backend Configuration**: The FastAPI application will be modified to parse the `--port` argument and use it to run the Uvicorn server.
4.  **Load URL**: Once the backend signals it's ready (e.g., by logging a specific message to `stdout`), the Electron `BrowserWindow` will load the URL `http://localhost:<found_port>`.

## 5. Application Distribution

`electron-builder` will be used to package the final application for distribution on Windows, macOS, and Linux.

**Configuration:**

-   The `package.json` file will contain a `build` section to configure `electron-builder`.
-   The `dist_backend` directory containing the Python executable will be included as an "extra resource". This ensures it is bundled correctly within the final application package (e.g., inside `YourApp.app/Contents/Resources/` on macOS).
-   Build scripts in `package.json` (e.g., `npm run dist`) will trigger `electron-builder` to generate the platform-specific installers (`.exe`, `.dmg`, `.AppImage`, etc.).
