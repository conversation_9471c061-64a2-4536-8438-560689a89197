# Electron应用适配说明

## 概述
本前端应用已针对Electron桌面应用进行了全面优化，提供了紧凑、高效的桌面应用体验。

## 主要适配特性

### 1. 窗口尺寸适配
- **固定窗口尺寸**: 100vw × 100vh，完全填充Electron窗口
- **左侧控制面板**: 380px宽度（从450px压缩）
- **右侧结果区域**: 自适应剩余宽度
- **禁用溢出**: 防止内容超出窗口边界

### 2. 紧凑化布局
- **减小间距**: 所有组件的内边距和外边距都进行了优化
- **字体大小**: 全局字体从14px调整为13px
- **组件高度**: 输入框、按钮等组件高度适配桌面应用
- **表格优化**: 行高和字体大小针对桌面显示优化

### 3. Element Plus组件适配
- **表单组件**: 输入框高度32px，字体12px
- **按钮组件**: 紧凑的内边距和字体大小
- **表格组件**: 行高和单元格内边距优化
- **标签页**: 高度和字体大小调整
- **对话框**: 边距和内边距优化

### 4. 滚动条优化
- **自定义样式**: 6px宽度的细滚动条
- **工业风格**: 橙色主题色彩
- **平滑交互**: 悬停效果和过渡动画

### 5. 性能优化
- **禁用文本选择**: 提升桌面应用体验
- **禁用拖拽**: 防止意外的拖拽操作
- **will-change优化**: 动画性能优化
- **溢出控制**: 精确的溢出处理

## 文件结构

### 样式文件
- `src/styles/electron.css` - Electron专用全局样式
- 各组件内的`.electron-app`样式块 - 组件级适配

### 检测逻辑
- `App.vue` - Electron环境检测和类名添加
- 多重检测机制确保可靠性

## 适配的组件

### 主要视图
- **SimulationView**: 主布局和面板尺寸适配
- **HistoryView**: 表格和按钮紧凑化

### 功能组件
- **InputForm**: 表单元素紧凑化，折叠面板优化
- **ResultsDashboard**: 标签页和下载对话框适配
- **AnimationPlayer**: 画布和控制器尺寸优化
- **ResultsCharts**: 图表容器高度适配（600px）

## 使用方法

### 1. 自动检测
应用会自动检测Electron环境并应用适配样式：
```javascript
const isElectron = window.isElectron ||
                  window.process?.type === 'renderer' ||
                  window.navigator?.userAgent?.includes('Electron') ||
                  window.location.protocol === 'file:';
```

### 2. 手动启用
如果需要手动启用Electron模式：
```javascript
document.body.classList.add('electron-app');
```

### 3. 样式覆盖
所有Electron适配样式都使用`.electron-app`前缀，确保只在Electron环境下生效。

## 建议的Electron窗口配置

```javascript
// main.js (Electron主进程)
const mainWindow = new BrowserWindow({
  width: 1440,
  height: 900,
  minWidth: 1200,
  minHeight: 800,
  resizable: true,
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    enableRemoteModule: false
  }
});
```

## 特殊功能

### 1. 右键菜单禁用
在Electron环境下自动禁用右键菜单，提供更原生的桌面应用体验。

### 2. 开发者工具控制
生产环境下禁用F12和Ctrl+Shift+I快捷键。

### 3. 窗口行为优化
- 禁用页面滚动
- 固定窗口内容
- 优化拖拽行为

## 兼容性
- 完全兼容Web浏览器环境
- 在非Electron环境下自动回退到标准样式
- 响应式设计保持不变

## 维护说明
- 新增组件时请添加对应的`.electron-app`样式适配
- 保持样式的一致性和紧凑性原则
- 测试时请同时验证Web和Electron环境
