{"name": "simulator-desktop-app", "version": "1.0.0", "description": "Desktop application for the simulator", "main": "main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development DEBUG=true electron .", "build-all": "pushd ..\\web_app\\frontend && npm run build && popd && rd /s /q ..\\web_app\\backend\\dist && xcopy /E /I /Y ..\\web_app\\frontend\\dist ..\\web_app\\backend\\dist\\ && pushd .. && uv run python -m PyInstaller --noconfirm --name=backend_server --add-data=\"web_app/backend/dist;dist\" --distpath=desktop_app/bin web_app/backend/run.py && popd && electron-builder", "build": "electron-builder"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "cross-env": "^7.0.3"}, "dependencies": {"portfinder": "^1.0.32", "ws": "^8.18.3"}, "build": {"extraResources": [{"from": "bin", "to": "bin"}, {"from": "sim.png", "to": "sim.png"}]}}