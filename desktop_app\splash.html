<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数码一体机凸轮优化仿真</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow: hidden;
        }

        .splash-container {
            text-align: center;
            animation: fadeIn 0.8s ease-out;
        }

        .app-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .app-subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .loader-container {
            position: relative;
            margin-bottom: 20px;
        }

        .loader {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ffffff;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 14px;
            opacity: 0.9;
            animation: pulse 2s ease-in-out infinite;
        }

        .progress-bar {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 20px auto 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f0f0f0);
            border-radius: 2px;
            animation: progress 3s ease-in-out infinite;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.9; }
            50% { opacity: 0.6; }
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .version {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 12px;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="app-icon">⚙️</div>
        <div class="app-title">数码一体机凸轮优化仿真</div>
        <div class="app-subtitle">Digital Cam Optimization Simulator</div>

        <div class="loader-container">
            <div class="loader"></div>
        </div>

        <div class="loading-text">正在启动应用...</div>

        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
    </div>

    <div class="version">v1.0.0</div>

    <script>
        // 动态更新加载文本
        const loadingTexts = [
            '正在启动应用...',
            '初始化后端服务...',
            '加载用户界面...',
            '准备就绪...'
        ];

        let currentIndex = 0;
        const loadingTextElement = document.querySelector('.loading-text');

        setInterval(() => {
            currentIndex = (currentIndex + 1) % loadingTexts.length;
            loadingTextElement.textContent = loadingTexts[currentIndex];
        }, 800);
    </script>
</body>
</html>
