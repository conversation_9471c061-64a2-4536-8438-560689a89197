<template>
  <div class="industrial-form">
    <el-form :model="form" label-position="top" class="simulation-form">
      <!-- 基础参数 -->
      <div class="form-section">
        <h3 class="section-title">
          <el-icon><Setting /></el-icon>
          基础参数
        </h3>

        <el-form-item label="卷针形状" class="form-item">
          <el-select v-model="form.pin_shape" placeholder="选择卷针形状" class="industrial-select">
            <el-option label="六边形" value="hexagon"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="旋转中心X偏移 (mm)" class="form-item">
          <el-input-number
            v-model="form.rotation_center_x_offset"
            :step="0.1"
            :precision="1"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="隔膜厚度 (mm)" class="form-item">
          <el-input-number
            v-model="form.film_thickness"
            :step="0.01"
            :precision="2"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="总旋转角度 (°)" class="form-item">
          <el-input-number
            v-model="form.total_rotation"
            :step="360"
            :min="360"
            :max="7200"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="角度步长 (°)" class="form-item">
          <el-input-number
            v-model="form.step_angle"
            :step="0.1"
            :precision="1"
            :min="0.1"
            :max="5.0"
            controls-position="right"
            class="industrial-input"
          ></el-input-number>
        </el-form-item>
      </div>

      <!-- 折叠面板区域 -->
      <el-collapse v-model="activeCollapse" class="form-collapse">
        <!-- 圆角参数 -->
        <el-collapse-item name="rounded" class="collapse-item">
          <template #title>
            <div class="collapse-title">
              <el-icon><Tools /></el-icon>
              <span>圆角设置</span>
            </div>
          </template>

          <div class="collapse-content">
            <el-form-item class="form-item">
              <template #label>
                <span class="switch-label">启用圆角仿真</span>
              </template>
              <el-switch
                v-model="form.use_rounded_corners"
                class="industrial-switch"
                active-color="#00b894"
                inactive-color="#636e72"
              ></el-switch>
            </el-form-item>

            <div v-if="form.use_rounded_corners" class="rounded-params">
              <el-form-item label="锐角圆角半径 (mm)" class="form-item">
                <el-input-number
                  v-model="form.sharp_radius"
                  :step="0.1"
                  :precision="1"
                  :min="0.1"
                  controls-position="right"
                  class="industrial-input"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="钝角圆角半径 (mm)" class="form-item">
                <el-input-number
                  v-model="form.blunt_radius"
                  :step="0.1"
                  :precision="1"
                  :min="0.1"
                  controls-position="right"
                  class="industrial-input"
                ></el-input-number>
              </el-form-item>
            </div>
          </div>
        </el-collapse-item>

        <!-- 过辊参数 -->
        <el-collapse-item name="roller" class="collapse-item">
          <template #title>
            <div class="collapse-title">
              <el-icon><Operation /></el-icon>
              <span>过辊配置</span>
            </div>
          </template>

          <div class="collapse-content">
            <div class="roller-grid">
              <el-form-item label="过辊A位置X (mm)" class="form-item">
                <el-input-number
                  v-model="form.roller_A_x"
                  :step="0.5"
                  :precision="1"
                  controls-position="right"
                  class="industrial-input"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="过辊A位置Y (mm)" class="form-item">
                <el-input-number
                  v-model="form.roller_A_y"
                  :step="0.5"
                  :precision="1"
                  controls-position="right"
                  class="industrial-input"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="过辊B位置X (mm)" class="form-item">
                <el-input-number
                  v-model="form.roller_B_x"
                  :step="0.5"
                  :precision="1"
                  controls-position="right"
                  class="industrial-input"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="过辊B位置Y (mm)" class="form-item">
                <el-input-number
                  v-model="form.roller_B_y"
                  :step="0.5"
                  :precision="1"
                  controls-position="right"
                  class="industrial-input"
                ></el-input-number>
              </el-form-item>
            </div>

            <el-form-item label="过辊半径 (mm)" class="form-item">
              <el-input-number
                v-model="form.roller_radius"
                :step="0.1"
                :precision="1"
                :min="0.5"
                controls-position="right"
                class="industrial-input"
              ></el-input-number>
            </el-form-item>
          </div>
        </el-collapse-item>

        <!-- 卷针顶点配置 -->
        <el-collapse-item name="vertices" class="collapse-item">
          <template #title>
            <div class="collapse-title">
              <el-icon><Grid /></el-icon>
              <span>卷针顶点坐标 (V1-V6)</span>
            </div>
          </template>

          <div class="collapse-content">
            <div class="vertices-compact">
              <div class="vertex-row" v-for="i in 6" :key="i">
                <span class="vertex-label">V{{ i }}:</span>
                <div class="vertex-coords-inline">
                  <span class="coord-label">X</span>
                  <el-input-number
                    v-model="form[`v${i}_x`]"
                    :step="0.5"
                    :precision="1"
                    controls-position="right"
                    class="industrial-input compact"
                    size="small"
                  ></el-input-number>
                  <span class="coord-label">Y</span>
                  <el-input-number
                    v-model="form[`v${i}_y`]"
                    :step="0.5"
                    :precision="1"
                    controls-position="right"
                    class="industrial-input compact"
                    size="small"
                  ></el-input-number>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 运行按钮 -->
      <el-form-item class="run-button-container">
        <el-button
          type="primary"
          @click="runSimulation"
          :loading="isRunning"
          :disabled="isRunning"
          class="run-button"
          size="large"
        >
          <el-icon class="button-icon"><VideoPlay /></el-icon>
          {{ isRunning ? '仿真运行中...' : '开始仿真' }}
        </el-button>
        <el-button
          @click="resetForm"
          :disabled="isRunning"
          class="reset-button"
          size="large"
        >
          <el-icon class="button-icon"><Refresh /></el-icon>
          重置参数
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { reactive, ref } from 'vue';
import { Setting, Tools, Operation, Grid, VideoPlay, Refresh } from '@element-plus/icons-vue';

export default {
  name: 'InputForm',
  components: {
    Setting,
    Tools,
    Operation,
    Grid,
    VideoPlay,
    Refresh,
  },
  props: {
    isRunning: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['run-simulation'],
  setup(props, { emit }) {
    // 折叠面板控制（默认全部折叠）
    const activeCollapse = ref([]);

   const defaultParameters = {
     pin_shape: 'hexagon',
     rotation_center_x_offset: 0.0,
     film_thickness: 0.1,
     total_rotation: 3600,
     step_angle: 0.5,
     use_rounded_corners: true,
     sharp_radius: 0.8,
     blunt_radius: 12.0,
     roller_A_x: 0.5,
     roller_A_y: 80.0,
     roller_B_x: -30.0,
     roller_B_y: 80.0,
     roller_radius: 2.0,
     v1_x: -30,
     v1_y: 0,
     v2_x: -20,
     v2_y: -4,
     v3_x: 20,
     v3_y: -4,
     v4_x: 30,
     v4_y: 0,
     v5_x: 20,
     v5_y: 4,
     v6_x: -20,
     v6_y: 4,
   };

    // 使用与main_new.py和hexagon_simulation.py一致的默认值
    const form = reactive({ ...defaultParameters });

    const runSimulation = () => {
      emit('run-simulation', { ...form });
    };

   const resetForm = () => {
     Object.assign(form, defaultParameters);
   };

    return {
      form,
      runSimulation,
      resetForm,
      isRunning: props.isRunning,
      activeCollapse,
    };
  },
};
</script>

<style scoped>
/* 传统工业风格表单样式 */
.industrial-form {
  background: transparent;
}

.simulation-form {
  background: transparent;
}

.form-section {
  margin-bottom: 25px;
  background: rgba(236, 240, 241, 0.95);
  border-radius: 6px;
  padding: 20px;
  border: 2px solid #95a5a6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.section-title {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  border-bottom: 3px solid #e67e22;
  padding-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #e67e22;
}

.form-item {
  margin-bottom: 18px;
}

.form-item :deep(.el-form-item__label) {
  color: #2c3e50 !important;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

/* 传统工业风格输入框 */
.industrial-input {
  width: 100%;
}

.industrial-input :deep(.el-input-number) {
  width: 100%;
}

.industrial-input :deep(.el-input__wrapper) {
  background: #ffffff !important;
  border: 2px solid #95a5a6 !important;
  border-radius: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.industrial-input :deep(.el-input__wrapper:hover) {
  border-color: #e67e22 !important;
  box-shadow: 0 0 5px rgba(230, 126, 34, 0.3);
}

.industrial-input :deep(.el-input__wrapper.is-focus) {
  border-color: #e67e22 !important;
  box-shadow: 0 0 8px rgba(230, 126, 34, 0.4);
}

.industrial-input :deep(.el-input__inner) {
  color: #2c3e50 !important;
  background: transparent !important;
  font-weight: 500;
}

.industrial-input :deep(.el-input-number__increase),
.industrial-input :deep(.el-input-number__decrease) {
  background: #ecf0f1 !important;
  border-color: #95a5a6 !important;
  color: #2c3e50 !important;
}

.industrial-input :deep(.el-input-number__increase:hover),
.industrial-input :deep(.el-input-number__decrease:hover) {
  background: #e67e22 !important;
  color: #ffffff !important;
}

/* 传统工业风格选择器 */
.industrial-select {
  width: 100%;
}

.industrial-select :deep(.el-select__wrapper) {
  background: #ffffff !important;
  border: 2px solid #95a5a6 !important;
  border-radius: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.industrial-select :deep(.el-select__wrapper:hover) {
  border-color: #e67e22 !important;
}

.industrial-select :deep(.el-select__selected-item) {
  color: #2c3e50 !important;
}

/* 传统工业风格开关 */
.switch-label {
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}

.industrial-switch :deep(.el-switch__core) {
  border: 2px solid #95a5a6;
  background: #ecf0f1;
}

.industrial-switch :deep(.el-switch__action) {
  background: #e67e22;
  box-shadow: 0 2px 4px rgba(230, 126, 34, 0.3);
}

/* 圆角参数动画 */
.rounded-params {
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 200px;
    transform: translateY(0);
  }
}

/* 过辊网格布局 */
.roller-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* 传统工业风格折叠面板样式 */
.form-collapse {
  margin-bottom: 25px;
  background: transparent;
}

.form-collapse :deep(.el-collapse) {
  background: transparent;
  border: none;
}

.form-collapse :deep(.el-collapse-item) {
  background: rgba(236, 240, 241, 0.95);
  border-radius: 6px;
  margin-bottom: 15px;
  border: 2px solid #95a5a6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.form-collapse :deep(.el-collapse-item__header) {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border: none;
  border-radius: 4px 4px 0 0;
  padding: 15px 20px;
  color: #e67e22;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-collapse :deep(.el-collapse-item__content) {
  padding: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0 0 4px 4px;
}

.collapse-title {
  display: flex;
  align-items: center;
  color: #e67e22;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.collapse-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.collapse-content {
  background: transparent;
}

/* 传统工业风格顶点配置布局 */
.vertices-compact {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.vertex-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: rgba(189, 195, 199, 0.3);
  border-radius: 4px;
  border: 1px solid #95a5a6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vertex-label {
  color: #27ae60;
  font-weight: 700;
  font-size: 14px;
  min-width: 30px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vertex-coords-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.coord-label {
  color: #2c3e50;
  font-size: 12px;
  font-weight: 600;
  min-width: 12px;
}

.industrial-input.compact {
  width: 80px;
}

.industrial-input.compact :deep(.el-input-number) {
  width: 80px;
  font-size: 12px;
}

.industrial-input.compact :deep(.el-input__wrapper) {
  padding: 4px 8px;
}

.industrial-input.small :deep(.el-input-number) {
  font-size: 13px;
}

/* 传统工业风格运行按钮 */
.run-button-container {
  margin: 30px 0 0 0;
 display: flex;
 justify-content: center;
 gap: 20px;
}

.run-button {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
  border: 3px solid #1e8449 !important;
  border-radius: 6px !important;
  padding: 15px 40px !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.run-button:hover {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-2px) !important;
  border-color: #27ae60 !important;
}

.run-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
}

.run-button.is-loading {
  background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  border-color: #7f8c8d !important;
}

.button-icon {
  margin-right: 8px;
  font-size: 18px;
}

.reset-button {
 background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%) !important;
 border: 3px solid #636e72 !important;
 color: white !important;
}

.reset-button:hover {
 background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%) !important;
 box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
 transform: translateY(-2px) !important;
 border-color: #7f8c8d !important;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .roller-grid,
  .vertices-grid {
    grid-template-columns: 1fr;
  }

  .form-section {
    padding: 15px;
  }

  .run-button {
    padding: 12px 30px !important;
    font-size: 14px !important;
  }
}
</style>

<style scoped>
/* Electron-specific compact layout styles */
.electron-app .form-section {
  margin-bottom: 15px;
  padding: 15px;
}

.electron-app .section-title {
  font-size: 14px;
  margin-bottom: 15px;
  padding-bottom: 8px;
}

.electron-app .form-item {
  margin-bottom: 12px;
}

.electron-app .form-item :deep(.el-form-item__label) {
  font-size: 12px;
  margin-bottom: 4px;
}

.electron-app .industrial-input.compact {
  width: 70px;
}

.electron-app .industrial-input.compact :deep(.el-input-number) {
  width: 70px;
}

.electron-app .vertex-row {
  padding: 8px 10px;
  gap: 8px;
}

.electron-app .vertex-label {
  font-size: 12px;
}

.electron-app .coord-label {
  font-size: 11px;
}

.electron-app .form-collapse {
  margin-bottom: 15px;
}

.electron-app .form-collapse :deep(.el-collapse-item) {
  margin-bottom: 10px;
}

.electron-app .form-collapse :deep(.el-collapse-item__header) {
  padding: 10px 15px;
}

.electron-app .collapse-title {
  font-size: 14px;
}

.electron-app .form-collapse :deep(.el-collapse-item__content) {
  padding: 15px;
}

.electron-app .run-button-container {
  margin-top: 20px;
}

.electron-app .run-button {
  padding: 12px 30px !important;
  font-size: 14px !important;
}
</style>
