from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from app.services.connection_manager import manager
import asyncio
import json
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


@router.websocket("/ws/{simulation_id}")
async def websocket_endpoint(websocket: WebSocket, simulation_id: int):
    await manager.connect(websocket, simulation_id)
    logger.info(f"WebSocket connected for simulation {simulation_id}")

    try:
        # 发送连接确认消息
        await websocket.send_text(
            json.dumps(
                {
                    "type": "connection",
                    "message": "WebSocket connected successfully",
                    "simulation_id": simulation_id,
                }
            )
        )

        # 心跳和消息处理循环
        while True:
            try:
                # 等待客户端消息或超时（心跳）
                message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)

                # 处理客户端消息
                try:
                    data = json.loads(message)
                    if data.get("type") == "ping":
                        # 响应心跳
                        await websocket.send_text(
                            json.dumps(
                                {"type": "pong", "timestamp": data.get("timestamp")}
                            )
                        )
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received from client: {message}")

            except asyncio.TimeoutError:
                # 发送心跳
                try:
                    await websocket.send_text(
                        json.dumps(
                            {"type": "heartbeat", "simulation_id": simulation_id}
                        )
                    )
                except Exception as e:
                    logger.error(f"Failed to send heartbeat: {e}")
                    break

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for simulation {simulation_id}")
    except Exception as e:
        logger.error(f"WebSocket error for simulation {simulation_id}: {e}")
    finally:
        manager.disconnect(websocket, simulation_id)
