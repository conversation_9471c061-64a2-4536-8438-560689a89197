<template>
  <div>
    <div class="results-header">
      <div class="simulation-info">
        <span v-if="simulationId" class="simulation-id-display">
          <el-icon><DataAnalysis /></el-icon>
          仿真ID: {{ simulationId }}
        </span>
        <span v-else class="no-simulation-text">
          <el-icon><Warning /></el-icon>
          暂无仿真数据
        </span>
      </div>
      <div class="header-actions">
        <el-button
          @click="showDownloadDialog"
          type="success"
          :disabled="!simulationData"
          class="download-btn"
        >
          <el-icon><Download /></el-icon>
          下载数据
        </el-button>
      </div>
    </div>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="数据图表" name="charts">
        <ResultsCharts :simulationData="simulationData" ref="resultsCharts" />
      </el-tab-pane>
      <el-tab-pane label="动画演示" name="animation">
        <AnimationPlayer :simulationData="simulationData" ref="animationPlayer" />
      </el-tab-pane>
    </el-tabs>

    <!-- 下载选择弹窗 -->
    <el-dialog
      v-model="downloadDialogVisible"
      title="选择下载数据"
      width="500px"
      :before-close="handleDialogClose"
    >
      <div class="download-options">
        <div class="option-section">
          <h4>选择要下载的数据字段：</h4>
          <el-checkbox-group v-model="selectedFields">
            <el-checkbox label="theta_deg" checked>旋转角度 (°)</el-checkbox>
            <el-checkbox label="S_total" checked>总长度 (mm)</el-checkbox>
            <el-checkbox label="L">切线长度 (mm)</el-checkbox>
            <el-checkbox label="S">包覆长度 (mm)</el-checkbox>
            <el-checkbox label="accumulated_thickness">累积厚度 (mm)</el-checkbox>
            <el-checkbox label="layer_numbers">层数</el-checkbox>
            <el-checkbox label="layer_consumption">每层消耗 (mm)</el-checkbox>
          </el-checkbox-group>
        </div>

        <div class="option-section">
          <h4>数据处理选项：</h4>
          <el-checkbox v-model="normalizeTotal">总长度从0开始（减去首个值）</el-checkbox>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="downloadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmDownload" :loading="isDownloading">
            {{ isDownloading ? '下载中...' : '确认下载' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, nextTick, watch } from 'vue';
import { DataAnalysis, Warning, Download } from '@element-plus/icons-vue';
import ResultsCharts from './ResultsCharts.vue';
import AnimationPlayer from './AnimationPlayer.vue';
import apiClient from '../api/client';

export default {
  name: 'ResultsDashboard',
  components: {
    ResultsCharts,
    AnimationPlayer,
    DataAnalysis,
    Warning,
    Download,
  },
  props: {
    simulationData: {
      type: Object,
      default: () => null,
    },
    simulationId: {
      type: Number,
      default: null,
    },
    simulationId: {
      type: Number,
      default: null,
    }
  },
  setup(props, { expose }) {
    const activeTab = ref('charts'); // 默认显示数据图表页面
    const animationPlayer = ref(null);
    const resultsCharts = ref(null);

    // 下载弹窗相关状态
    const downloadDialogVisible = ref(false);
    const selectedFields = ref(['theta_deg', 'S_total']); // 默认选择角度和总长度
    const normalizeTotal = ref(true); // 默认启用总长度从0开始
    const isDownloading = ref(false);

    const initializeAnimation = async () => {
      // Wait for next tick to ensure DOM is ready
      await nextTick();

      // Check if animationPlayer ref exists and has the correct method
      if (animationPlayer.value && animationPlayer.value.initializeAnimation) {
        try {
          await animationPlayer.value.initializeAnimation();
          console.log('Animation initialized successfully');
        } catch (error) {
          console.error('Error initializing animation:', error);
        }
      } else {
        console.error('AnimationPlayer ref not available or initializeAnimation method not found');
      }
    };

    const showDownloadDialog = () => {
      downloadDialogVisible.value = true;
    };

    const handleDialogClose = () => {
      downloadDialogVisible.value = false;
    };

    const confirmDownload = async () => {
      if (!props.simulationId || selectedFields.value.length === 0) return;

      isDownloading.value = true;
      try {
        const downloadOptions = {
          fields: selectedFields.value,
          normalize_total: normalizeTotal.value
        };

        const response = await apiClient.exportSimulationResults(props.simulationId, downloadOptions);
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `simulation_${props.simulationId}_results.xlsx`);
        document.body.appendChild(link);
        link.click();

        downloadDialogVisible.value = false;
      } catch (error) {
        console.error('Failed to download results:', error);
      } finally {
        isDownloading.value = false;
      }
    };

    // 监听标签页切换，处理图表渲染问题
    watch(activeTab, async (newTab) => {
      if (newTab === 'charts') {
        // 切换到图表页面时，延迟重新渲染图表以确保容器尺寸正确
        await nextTick();
        setTimeout(() => {
          if (resultsCharts.value && resultsCharts.value.resizeChart) {
            resultsCharts.value.resizeChart();
          }
        }, 100);
      }
    });

    // Expose the initializeAnimation method to parent
    expose({
      initializeAnimation
    });

    return {
      activeTab,
      resultsCharts,
      animationPlayer,
      initializeAnimation,
      // 下载弹窗相关
      downloadDialogVisible,
      selectedFields,
      normalizeTotal,
      isDownloading,
      showDownloadDialog,
      handleDialogClose,
      confirmDownload,
    };
  },
};
</script>

<style scoped>
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
  border-radius: 8px;
  border: 2px solid #95a5a6;
}

.simulation-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.simulation-id-display {
  color: #27ae60;
  font-weight: 700;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.no-simulation-text {
  color: #7f8c8d;
  font-style: italic;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.download-btn {
  background: #27ae60 !important;
  border: 2px solid #27ae60 !important;
  color: #ffffff !important;
  padding: 10px 20px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  display: flex;
  align-items: center;
  gap: 6px;
}

.download-btn:hover {
  background: #229954 !important;
  border-color: #229954 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.download-btn:disabled {
  background: #95a5a6 !important;
  border-color: #95a5a6 !important;
  color: #ecf0f1 !important;
  transform: none !important;
  box-shadow: none !important;
}

.download-options {
  padding: 10px 0;
}

.option-section {
  margin-bottom: 20px;
}

.option-section h4 {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
  border-bottom: 2px solid #e67e22;
  padding-bottom: 5px;
}

.option-section :deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-section :deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 0;
}

.option-section :deep(.el-checkbox__label) {
  color: #2c3e50;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Electron应用结果面板适配样式 */
.electron-app .download-options {
  padding: 8px 0;
}

.electron-app .option-section {
  margin-bottom: 15px;
}

.electron-app .option-section h4 {
  font-size: 13px;
  margin-bottom: 8px;
  padding-bottom: 4px;
}

.electron-app .option-section :deep(.el-checkbox-group) {
  gap: 6px;
}

.electron-app .option-section :deep(.el-checkbox__label) {
  font-size: 12px;
}

/* 标签页紧凑样式 */
.electron-app :deep(.el-tabs__header) {
  margin-bottom: 10px;
}

.electron-app :deep(.el-tabs__item) {
  font-size: 13px;
  padding: 0 15px;
  height: 35px;
  line-height: 35px;
}

.electron-app :deep(.el-tabs__content) {
  padding: 10px 0;
}

/* Electron环境下的紧凑样式 */
.electron-app .results-header {
  padding: 10px 15px;
  margin-bottom: 15px;
}

.electron-app .simulation-id-display {
  font-size: 14px;
}

.electron-app .no-simulation-text {
  font-size: 12px;
}

.electron-app .download-btn {
  padding: 8px 16px !important;
  font-size: 12px !important;
}

/* 下载按钮紧凑样式 */
.electron-app :deep(.el-button) {
  font-size: 12px;
  padding: 8px 16px;
}
</style>
