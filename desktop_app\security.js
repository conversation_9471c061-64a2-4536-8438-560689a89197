// Electron安全配置模块
const { session } = require('electron');

/**
 * 配置Electron应用的安全设置
 */
function setupSecurity() {
  const isDevelopment = process.env.NODE_ENV === 'development' || process.env.DEBUG;
  // 设置Content Security Policy
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    let csp = "default-src 'self'; " +
              "script-src 'self' 'unsafe-inline'; " +
              "style-src 'self' 'unsafe-inline'; " +
              "img-src 'self' data: blob:; " +
              "font-src 'self' data:; " +
              "connect-src 'self' http://127.0.0.1:* http://localhost:* ws://127.0.0.1:* ws://localhost:*; " +
              "object-src 'none'; " +
              "base-uri 'self'; " +
              "form-action 'self';";

    // 在开发模式下放宽CSP以支持开发者工具
    if (isDevelopment) {
      csp += " script-src 'self' 'unsafe-inline' 'unsafe-eval' https://chrome-devtools-frontend.appspot.com; " +
             "style-src 'self' 'unsafe-inline' https://chrome-devtools-frontend.appspot.com; " +
             "connect-src 'self' http://127.0.0.1:* http://localhost:* ws://127.0.0.1:* ws://localhost:* https://chrome-devtools-frontend.appspot.com;";
    }

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [csp]
      }
    });
  });

  // 阻止新窗口创建（防止恶意弹窗）
  session.defaultSession.setPermissionRequestHandler((_, permission, callback) => {
    // 拒绝所有权限请求，除非明确需要
    const allowedPermissions = []; // 根据需要添加权限
    callback(allowedPermissions.includes(permission));
  });

  // 阻止导航到外部URL（开发模式下完全禁用）
  if (!isDevelopment) {
    session.defaultSession.webRequest.onBeforeRequest((details, callback) => {
      const url = new URL(details.url);

      // 只允许本地服务器和文件协议，以及开发者工具
      if (url.protocol === 'http:' || url.protocol === 'https:') {
        if (url.hostname === '127.0.0.1' || url.hostname === 'localhost' ||
            url.hostname === 'chrome-devtools-frontend.appspot.com') {
          callback({ cancel: false });
        } else {
          console.warn('Blocked external request:', details.url);
          callback({ cancel: true });
        }
      } else if (url.protocol === 'file:' || url.protocol === 'data:' ||
                 url.protocol === 'blob:' || url.protocol === 'devtools:') {
        callback({ cancel: false });
      } else {
        console.warn('Blocked request with protocol:', url.protocol);
        callback({ cancel: true });
      }
    });
  } else {
    console.log('Development mode: All security restrictions disabled for DevTools');
  }

  console.log('Electron security measures applied');
}

/**
 * 获取安全的BrowserWindow配置
 */
function getSecureWindowOptions(additionalOptions = {}) {
  const isDevelopment = process.env.NODE_ENV === 'development' || process.env.DEBUG;

  return {
    webPreferences: {
      nodeIntegration: false, // 主进程中禁用
      nodeIntegrationInWorker: false,
      nodeIntegrationInSubFrames: false,
      contextIsolation: true, // 启用上下文隔离
      enableRemoteModule: false,
      webSecurity: !isDevelopment, // 开发模式下禁用webSecurity以支持DevTools
      allowRunningInsecureContent: isDevelopment, // 开发模式下允许
      experimentalFeatures: isDevelopment, // 开发模式下允许实验性功能
      plugins: false,
      webgl: true, // 允许WebGL用于图表渲染
      webaudio: false,
      backgroundThrottling: false,
      devTools: isDevelopment, // 只在开发模式下启用开发者工具
      sandbox: false, // 禁用沙箱以允许preload脚本访问Node.js模块
      ...additionalOptions.webPreferences
    },
    show: false, // 等待ready-to-show事件
    autoHideMenuBar: true,
    ...additionalOptions
  };
}

module.exports = {
  setupSecurity,
  getSecureWindowOptions
};
