<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 750px; /* Default height */
}

.electron-app .chart-container {
  height: 600px; /* Compact height for Electron */
}
</style>

<script>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';

export default {
  name: 'ResultsCharts',
  props: {
    simulationData: {
      type: Object,
      default: () => null,
    },
  },
  setup(props, { expose }) {
    const chartContainer = ref(null);
    let chart = null;

    const renderChart = () => {
      if (!props.simulationData || !chartContainer.value) {
        return;
      }
      if (chart) {
        chart.dispose();
      }
      chart = echarts.init(chartContainer.value);

      const { theta_deg, L, S, S_total, accumulated_thickness, layer_numbers, layer_consumption } = props.simulationData;

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['切线长度', '包覆长度', '总长度', '累积厚度', '层数', '每层消耗'],
          selected: {
            '切线长度': true,
            '包覆长度': true,
            '总长度': true,
            '累积厚度': false,
            '层数': false,
            '每层消耗': false,
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          data: theta_deg,
          axisLine: { onZero: false },
          name: '旋转角度 (°)'
        },
        yAxis: [
          {
            name: '长度 (mm)',
            type: 'value',
          },
          {
            name: '厚度 (mm)',
            type: 'value',
            position: 'right',
            offset: 80,
          },
          {
            name: '层数',
            type: 'value',
            position: 'right',
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 100,
          },
          {
            type: 'inside',
            start: 0,
            end: 100,
          }
        ],
        series: [
          { name: '切线长度', type: 'line', yAxisIndex: 0, data: L, showSymbol: false },
          { name: '包覆长度', type: 'line', yAxisIndex: 0, data: S, showSymbol: false },
          { name: '总长度', type: 'line', yAxisIndex: 0, data: S_total, showSymbol: false },
          { name: '累积厚度', type: 'line', yAxisIndex: 1, data: accumulated_thickness, showSymbol: false },
          { name: '层数', type: 'line', yAxisIndex: 2, data: layer_numbers, showSymbol: false },
          { name: '每层消耗', type: 'line', yAxisIndex: 0, data: layer_consumption, showSymbol: false },
        ],
      };
      chart.setOption(option);
    };

    const resizeChart = () => {
      if (chart) {
        chart.resize();
      }
    };

    // 窗口大小变化时重新调整图表大小
    const handleResize = () => {
      resizeChart();
    };

    onMounted(() => {
      renderChart();
      window.addEventListener('resize', handleResize);
    });

    onUnmounted(() => {
      if (chart) {
        chart.dispose();
      }
      window.removeEventListener('resize', handleResize);
    });

    watch(() => props.simulationData, renderChart);

    // 暴露方法给父组件
    expose({
      resizeChart
    });

    return {
      chartContainer,
    };
  },
};
</script>
