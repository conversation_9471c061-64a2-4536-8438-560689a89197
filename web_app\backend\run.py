import uvicorn
import argparse
import signal
import sys
import logging
from app import main # noqa: E402

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    parser = argparse.ArgumentParser(
        description="FastAPI server for Hexagon Simulation"
    )
    parser.add_argument(
        "--port", type=int, default=8888, help="Port to run the server on"
    )
    parser.add_argument(
        "--reload", action="store_true", help="Enable auto-reload for development"
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    args = parser.parse_args()

    logger.info(f"Starting server on port {args.port}")
    logger.info(f"Reload: {args.reload}, Debug: {args.debug}")

    try:
        uvicorn.run(
            "app.main:app",  # 使用字符串形式以支持reload
            host="0.0.0.0",
            port=args.port,
            reload=args.reload,
            log_level="debug" if args.debug else "info",
            access_log=True,
            use_colors=True,
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)
