ri<template>
  <div class="industrial-layout">
    <el-container class="main-container">
      <el-aside width="450px" class="control-panel">
        <div class="panel-header">
          <h2 class="panel-title">
            <el-icon class="title-icon"><Setting /></el-icon>
            仿真参数配置
          </h2>
        </div>
        <div class="panel-content">
          <InputForm @run-simulation="handleRunSimulation" :is-running="isRunning" />
          <HistoryView ref="historyViewRef" @select-simulation="loadSimulation" @reconnect="handleReconnect" />
        </div>
      </el-aside>
      <el-main class="results-area">
        <div class="results-header">
          <h2 class="results-title">
            <el-icon class="title-icon"><DataAnalysis /></el-icon>
            仿真结果分析
          </h2>
          <div class="header-actions">
           <el-button @click="showLogs = !showLogs" size="small" class="log-toggle-btn">
             <el-icon style="margin-right: 4px;"><View /></el-icon>
             {{ showLogs ? '隐藏日志' : '显示日志' }}
           </el-button>
           <div class="status-indicator" :class="{ 'running': isRunning, 'completed': simulationData }">
             <span class="status-dot"></span>
             <span class="status-text">
               {{ isRunning ? '计算中' : simulationData ? '已完成' : '待运行' }}
             </span>
           </div>
          </div>
        </div>
        <div class="results-content">
          <div class="dashboard-container">
           <ResultsDashboard
             v-if="simulationData && simulationData.theta"
             ref="resultsDashboard"
             :simulationData="simulationData"
             :simulationId="simulationId"
           />
           <el-empty
             v-else
             description="请配置参数并运行仿真，或从历史记录中选择一个仿真结果"
             class="empty-state"
           >
             <template #image>
               <el-icon class="empty-icon"><Monitor /></el-icon>
             </template>
           </el-empty>
          </div>
          <div v-if="showLogs" class="log-container">
            <LogViewer :logs="logs" @close="showLogs = false" />
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { ref, nextTick } from 'vue';
import { ElNotification } from 'element-plus';
import { Setting, DataAnalysis, Monitor, View } from '@element-plus/icons-vue';
import InputForm from '../components/InputForm.vue';
import ResultsDashboard from '../components/ResultsDashboard.vue';
import HistoryView from './HistoryView.vue';
import LogViewer from '../components/LogViewer.vue';
import apiClient from '../api/client';
import { webSocketService } from '../api/websocket';

export default {
  name: 'SimulationView',
  components: {
    InputForm,
    ResultsDashboard,
    HistoryView,
    LogViewer,
    Setting,
    DataAnalysis,
    Monitor,
    View,
  },
  setup() {
    const simulationData = ref(null);
    const simulationStatus = ref('');
    const simulationId = ref(null);
    const isRunning = ref(false);
    const logs = ref([]);
    const showLogs = ref(false);
    const resultsDashboard = ref(null);
    const historyViewRef = ref(null);

    const handleRunSimulation = async (params) => {
      try {
        simulationData.value = null;
        isRunning.value = true;
        simulationStatus.value = 'running';
        logs.value = [];
        showLogs.value = true;
        const response = await apiClient.startSimulation(params);
        simulationId.value = response.data.id;
        if (historyViewRef.value) {
           historyViewRef.value.refreshHistory();
        }
        ElNotification({
          title: '仿真启动成功',
          message: `仿真任务已创建，ID: ${simulationId.value}`,
          type: 'success',
        });
        webSocketService.connect(simulationId.value, handleWebSocketMessage);
      } catch (error) {
        console.error('Failed to start simulation:', error);
        simulationStatus.value = 'failed';
        isRunning.value = false;
        ElNotification({
          title: '错误',
          message: '仿真启动失败',
          type: 'error',
        });
      }
    };

    const fetchResults = async (id) => {
      try {
        console.log(`Fetching results for simulation ${id}...`);
        const response = await apiClient.getSimulationResults(id);
        simulationData.value = response.data;
        console.log('Results data loaded, initializing UI...');

        await nextTick();

        // 安全地初始化动画，添加超时和错误处理
        if (resultsDashboard.value) {
          try {
            console.log('Initializing animation...');
            await Promise.race([
              resultsDashboard.value.initializeAnimation(),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Animation initialization timeout')), 5000)
              )
            ]);
            console.log('Animation initialized successfully');
          } catch (animError) {
            console.warn('Animation initialization failed:', animError);
            // 动画初始化失败不应该阻止整个流程
          }
        }

        ElNotification({
          title: '成功',
          message: '仿真结果加载完成',
          type: 'success',
        });
        console.log('Results fetch completed successfully');
      } catch (error) {
        console.error('Failed to fetch results:', error);
        ElNotification({
          title: '错误',
          message: '获取仿真结果失败',
          type: 'error',
        });
      }
    };

    const handleWebSocketMessage = (message) => {
      console.log('Received WebSocket message:', message);

      if (message.startsWith('status:')) {
        const status = message.split(':')[1];
        console.log(`Status update: ${status}`);
        simulationStatus.value = status;

        if (status === 'completed' || status === 'failed') {
          console.log('Simulation finished, cleaning up...');
          isRunning.value = false;
          showLogs.value = false;

          // 立即断开WebSocket连接
          webSocketService.disconnect(simulationId.value);
          console.log('WebSocket disconnected');

          // 异步更新历史记录，不阻塞主流程
          if (historyViewRef.value) {
            setTimeout(() => {
              historyViewRef.value.refreshHistory();
            }, 100);
          }

          if (status === 'completed') {
            console.log('Fetching results...');
            // 异步获取结果，不阻塞消息处理
            setTimeout(() => {
              fetchResults(simulationId.value);
            }, 200);
          } else {
            ElNotification({
              title: '错误',
              message: '仿真执行失败',
              type: 'error',
            });
          }
        }
      } else {
        // 处理日志消息
        try {
          const logData = JSON.parse(message);
          if (logData.level && logData.message) {
            logs.value.push(`[${logData.level}] ${logData.message}`);
          } else {
            logs.value.push(message);
          }
        } catch {
          logs.value.push(message);
        }
      }
    };

    const loadSimulation = async (id) => {
      simulationId.value = id;
      await fetchResults(id);
    };

    const handleReconnect = (id) => {
      simulationId.value = id;
      isRunning.value = true;
      simulationStatus.value = 'running';
      logs.value = [`Reconnecting to simulation ${id}...`];
      showLogs.value = true;
      webSocketService.connect(id, handleWebSocketMessage);
    };

    return {
      simulationData,
      simulationStatus,
      simulationId,
      isRunning,
      logs,
      showLogs,
      handleRunSimulation,
      loadSimulation,
      handleReconnect,
      resultsDashboard,
      historyViewRef,
    };
  },
};
</script>

<style scoped>
/* 传统工业风格主题 */
.industrial-layout {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  min-height: 100vh;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

.main-container {
  height: 100vh;
  background: transparent;
}

/* 控制面板样式 */
.control-panel {
  background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%);
  border-right: 4px solid #e67e22;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.4);
  position: relative;
  display: flex;
  flex-direction: column;
}

.control-panel::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #e67e22 0%, #d35400 50%, #e67e22 100%);
  animation: pulse 3s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

.panel-header {
  padding: 25px 20px 15px;
  border-bottom: 3px solid rgba(230, 126, 34, 0.4);
  background: rgba(0, 0, 0, 0.3);
}

.panel-title {
  color: #e67e22;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.title-icon {
  margin-right: 10px;
  font-size: 20px;
}

.panel-content {
  padding: 20px;
  height: calc(100vh - 70px);
  overflow-y: auto;
}

/* 结果区域样式 */
.results-area {
  background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
  padding: 0;
  position: relative;
}

.results-header {
  background: linear-gradient(90deg, #34495e 0%, #2c3e50 100%);
  padding: 20px 30px;
  border-bottom: 4px solid #27ae60;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.3);
}

.results-title {
  color: #27ae60;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.log-toggle-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ecf0f1 !important;
  font-weight: 600;
}

.log-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: #27ae60 !important;
  color: #27ae60 !important;
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  background: #636e72;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-indicator.running .status-dot {
  background: #f39c12;
  box-shadow: 0 0 8px rgba(243, 156, 18, 0.6);
}

.status-indicator.completed .status-dot {
  background: #27ae60;
  box-shadow: 0 0 8px rgba(39, 174, 96, 0.6);
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.status-text {
  color: #dfe6e9;
  font-size: 14px;
  font-weight: 500;
}

.results-content {
  position: relative;
  height: calc(100vh - 80px);
  padding: 25px;
  background: rgba(255, 255, 255, 0.05);
  overflow: hidden;
}

.dashboard-container {
  height: 100%;
  overflow-y: auto;
}

.log-container {
   position: absolute;
   bottom: 25px;
   left: 25px;
   right: 25px;
   z-index: 10;
}

/* 空状态样式 */
.empty-state {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px dashed #b2bec3;
}

.empty-icon {
  font-size: 64px;
  color: #636e72;
  margin-bottom: 20px;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.results-content::-webkit-scrollbar {
  width: 8px;
}

.panel-content::-webkit-scrollbar-track,
.results-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb,
.results-content::-webkit-scrollbar-thumb {
  background: rgba(116, 185, 255, 0.6);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.results-content::-webkit-scrollbar-thumb:hover {
  background: rgba(116, 185, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .control-panel {
    width: 400px !important;
  }
}

@media (max-width: 1200px) {
  .control-panel {
    width: 350px !important;
  }

  .panel-title {
    font-size: 16px;
  }

  .results-title {
    font-size: 18px;
  }
}

@media (max-width: 1000px) {
  .control-panel {
    width: 320px !important;
  }

  .panel-content {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
  }

  .control-panel {
    width: 100% !important;
    height: auto;
    max-height: 50vh;
    overflow-y: auto;
  }

  .results-area {
    height: 50vh;
  }
}

/* Electron应用主视图适配样式 */
.electron-app .industrial-layout {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}

.electron-app .main-container {
  height: 100vh;
}

.electron-app .control-panel {
  width: 380px;
  min-width: 380px;
}

.electron-app .panel-header {
  padding: 15px 15px 10px;
}

.electron-app .panel-title {
  font-size: 16px;
}

.electron-app .panel-content {
  height: calc(100vh - 70px);
  padding: 15px;
  overflow-y: auto;
}

.electron-app .results-area {
  width: calc(100vw - 380px);
  padding: 0;
}

.electron-app .results-header {
  padding: 15px 20px 10px;
}

.electron-app .results-title {
  font-size: 16px;
}

.electron-app .results-content {
  height: calc(100vh - 70px);
  padding: 10px 15px;
  overflow-y: auto;
}

.electron-app .status-indicator {
  font-size: 12px;
}

.electron-app .status-dot {
  width: 8px;
  height: 8px;
}

.electron-app .empty-state {
  height: calc(100vh - 150px);
}

.electron-app .empty-state :deep(.el-empty__description) {
  font-size: 13px;
}

.electron-app .empty-icon {
  font-size: 48px;
}

/* 加载状态优化 */
.electron-app .el-loading-mask {
  background-color: rgba(45, 52, 54, 0.7) !important;
}

.electron-app .el-loading-text {
  font-size: 13px !important;
}

/* 滚动条优化 */
.electron-app .panel-content::-webkit-scrollbar,
.electron-app .results-content::-webkit-scrollbar {
  width: 6px;
}

.electron-app .panel-content::-webkit-scrollbar-track,
.electron-app .results-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.electron-app .panel-content::-webkit-scrollbar-thumb,
.electron-app .results-content::-webkit-scrollbar-thumb {
  background: rgba(230, 126, 34, 0.6);
  border-radius: 3px;
}

.electron-app .panel-content::-webkit-scrollbar-thumb:hover,
.electron-app .results-content::-webkit-scrollbar-thumb:hover {
  background: rgba(230, 126, 34, 0.8);
}
</style>
