import axios from 'axios';

const apiClient = axios.create({
  baseURL: `${window.location.origin}/api/v1`, // The base URL is dynamically set from the window's location
  headers: {
    'Content-Type': 'application/json',
  },
});

export default {
  startSimulation(params) {
    return apiClient.post('/simulations/', params);
  },
  getSimulationStatus(simulationId) {
    return apiClient.get(`/simulations/${simulationId}/status`);
  },
  getSimulationResults(simulationId) {
    return apiClient.get(`/simulations/${simulationId}/results`);
  },
  getSimulations() {
    return apiClient.get('/simulations/');
  },
  exportSimulationResults(simulationId, options = {}) {
    return apiClient.post(`/simulations/${simulationId}/export`, options, {
      responseType: 'blob', // Important for file downloads
    });
  },
  deleteSimulation(simulationId) {
    return apiClient.delete(`/simulations/${simulationId}`);
  },
};
